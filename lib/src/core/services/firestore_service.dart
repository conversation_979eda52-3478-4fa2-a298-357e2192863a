import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:opti_tickets/src/screens/auth/models/user_model.dart';
import 'package:opti_tickets/src/screens/signatures/models/signature_model.dart';
import 'package:xr_helper/xr_helper.dart';

class FirestoreService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Collections
  static const String _usersCollection = 'users';
  static const String _signaturesCollection = 'signatures';
  static const String _clientsCollection = 'clients';
  static const String _plansCollection = 'plans';

  // * Authentication Methods ================================

  // Register user with email and password
  static Future<UserModel?> registerUser({
    required String email,
    required String password,
    required String name,
    bool isAdmin = false,
  }) async {
    try {
      final UserCredential userCredential =
          await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (userCredential.user != null) {
        final user = UserModel(
          uid: userCredential.user!.uid,
          name: name,
          email: email,
          isAdmin: isAdmin,
          password: password,
        );

        // Save user data to Firestore
        await _firestore
            .collection(_usersCollection)
            .doc(user.uid)
            .set(user.toFirestoreJson());

        // Save to local storage
        await GetStorageService.setData(
          key: LocalKeys.user,
          value: user.toJson(),
        );

        Log.i('User registered successfully: ${user.email}');
        return user;
      }
    } on FirebaseAuthException catch (e) {
      Log.e('Registration error: ${e.message}');
      throw Exception(e.message ?? 'Registration failed');
    } catch (e) {
      Log.e('Registration error: $e');
      throw Exception('Registration failed');
    }
    return null;
  }

  // Login user with email and password
  static Future<UserModel?> loginUser({
    required String email,
    required String password,
  }) async {
    try {
      final UserCredential userCredential =
          await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (userCredential.user != null) {
        // Get user data from Firestore
        final userDoc = await _firestore
            .collection(_usersCollection)
            .doc(userCredential.user!.uid)
            .get();

        if (userDoc.exists) {
          final user = UserModel.fromJson(userDoc.data()!);

          // Save to local storage
          await GetStorageService.setData(
            key: LocalKeys.user,
            value: user.toJson(),
          );

          Log.i('User logged in successfully: ${user.email}');
          return user;
        }
      }
    } on FirebaseAuthException catch (e) {
      Log.e('Login error: ${e.message}');
      throw Exception(e.message ?? 'Login failed');
    } catch (e) {
      Log.e('Login error: $e');
      throw Exception('Login failed');
    }
    return null;
  }

  // Logout user
  static Future<void> logoutUser() async {
    try {
      await _auth.signOut();
      await GetStorageService.clearLocalData();
      Log.i('User logged out successfully');
    } catch (e) {
      Log.e('Logout error: $e');
      throw Exception('Logout failed');
    }
  }

  // Get current user
  static User? get currentUser => _auth.currentUser;

  // * Signature Methods ================================

  // Add signature to Firestore
  static Future<bool> addSignature(SignatureModel signature) async {
    try {
      final docRef = _firestore.collection(_signaturesCollection).doc();
      final signatureWithId = signature.copyWith(id: docRef.id);

      await docRef.set(signatureWithId.toJson());

      Log.i('Signature added successfully: ${signature.place}');
      return true;
    } catch (e) {
      Log.e('Add signature error: $e');
      throw Exception('Failed to add signature');
    }
  }

  // Get signatures for a specific user and date
  static Future<List<SignatureModel>> getSignaturesByUserAndDate({
    required String uid,
    required String date,
  }) async {
    try {
      final querySnapshot = await _firestore
          .collection(_signaturesCollection)
          .where('uid', isEqualTo: uid)
          .where('date', isEqualTo: date)
          .orderBy('timestamp', descending: false)
          .get();

      return querySnapshot.docs
          .map((doc) => SignatureModel.fromJson(doc.data()))
          .toList();
    } catch (e) {
      Log.e('Get signatures error: $e');
      return [];
    }
  }

  // Get signatures for date range (admin use)
  static Future<List<SignatureModel>> getSignaturesByDateRange({
    required String startDate,
    required String endDate,
    String? uid,
  }) async {
    try {
      Query query = _firestore
          .collection(_signaturesCollection)
          .where('date', isGreaterThanOrEqualTo: startDate)
          .where('date', isLessThanOrEqualTo: endDate);

      if (uid != null && uid.isNotEmpty) {
        query = query.where('uid', isEqualTo: uid);
      }

      final querySnapshot = await query
          .orderBy('date', descending: true)
          .orderBy('timestamp', descending: false)
          .get();

      return querySnapshot.docs
          .map((doc) =>
              SignatureModel.fromJson(doc.data() as Map<String, dynamic>))
          .toList();
    } catch (e) {
      Log.e('Get signatures by date range error: $e');
      return [];
    }
  }

  // Check if user has first signature of the day
  static Future<bool> hasFirstSignatureOfDay({
    required String uid,
    required String date,
  }) async {
    try {
      final querySnapshot = await _firestore
          .collection(_signaturesCollection)
          .where('uid', isEqualTo: uid)
          .where('date', isEqualTo: date)
          .where('isFirstOfDay', isEqualTo: true)
          .limit(1)
          .get();

      return querySnapshot.docs.isNotEmpty;
    } catch (e) {
      Log.e('Check first signature error: $e');
      return false;
    }
  }

  // Get all users (for admin dropdown)
  static Future<List<UserModel>> getAllUsers() async {
    try {
      final querySnapshot = await _firestore
          .collection(_usersCollection)
          .where('isAdmin', isEqualTo: false)
          .orderBy('name')
          .get();

      return querySnapshot.docs
          .map((doc) => UserModel.fromJson(doc.data()))
          .toList();
    } catch (e) {
      Log.e('Get all users error: $e');
      return [];
    }
  }

  // * Stream Methods ================================

  // Generic stream method for filtering by 2 fields
  static Stream<List<T>> getStreamDataBy2Fields<T>({
    required String collection,
    required String field1,
    required String value1,
    required String field2,
    required value2,
    required T Function(String id, Map<String, dynamic> data) builder,
  }) {
    Log.i('Collection: $collection - Field1: $field1 - Value1: $value1 - '
        'Field2: $field2 - Value2: $value2');
    return _firestore
        .collection(collection)
        .where(field1, isEqualTo: value1)
        .where(field2, isEqualTo: value2)
        .orderBy('timestamp', descending: false)
        .snapshots()
        .map((snapshot) =>
            snapshot.docs.map((doc) => builder(doc.id, doc.data())).toList());
  }

  // Get signatures stream for specific user and date
  static Stream<List<SignatureModel>> getSignaturesStream({
    required String uid,
    required String date,
  }) {
    return getStreamDataBy2Fields<SignatureModel>(
      collection: _signaturesCollection,
      field1: 'uid',
      value1: uid,
      field2: 'date',
      value2: date,
      builder: (id, data) {
        return SignatureModel.fromJson(data);
      },
    );
  }

  // Get signatures stream for date range (admin use)
  static Stream<List<SignatureModel>> getSignaturesByDateRangeStream({
    required String startDate,
    required String endDate,
    String? uid,
  }) {
    Query query = _firestore
        .collection(_signaturesCollection)
        .where('date', isGreaterThanOrEqualTo: startDate)
        .where('date', isLessThanOrEqualTo: endDate);

    if (uid != null && uid.isNotEmpty) {
      query = query.where('uid', isEqualTo: uid);
    }

    return query
        .orderBy('date', descending: true)
        .orderBy('timestamp', descending: false)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) =>
                SignatureModel.fromJson(doc.data() as Map<String, dynamic>))
            .toList());
  }
}
