import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:opti_tickets/src/screens/signatures/models/plan_model.dart';
import 'package:opti_tickets/src/screens/signatures/models/signature_model.dart';
import 'package:xr_helper/xr_helper.dart';

class MigrationService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _signaturesCollection = 'signatures';
  static const String _plansCollection = 'plans';
  static const String _backupCollection = 'signatures_backup';

  /// Migrates signatures to plans.attendances structure
  /// This migration:
  /// 1. Creates backup of existing signatures
  /// 2. Groups signatures by mandob, client, and date
  /// 3. Creates plan documents with attendances array
  /// 4. Excludes any biometric data from migration
  static Future<bool> migrateSignaturesToPlans({
    bool createBackup = true,
    bool dryRun = false,
  }) async {
    try {
      Log.i('Starting migration from signatures to plans...');
      
      if (createBackup && !dryRun) {
        Log.i('Creating backup of signatures...');
        await _createSignaturesBackup();
      }

      // Get all signatures
      final signaturesSnapshot = await _firestore
          .collection(_signaturesCollection)
          .orderBy('timestamp')
          .get();

      if (signaturesSnapshot.docs.isEmpty) {
        Log.i('No signatures found to migrate');
        return true;
      }

      Log.i('Found ${signaturesSnapshot.docs.length} signatures to migrate');

      // Convert to SignatureModel objects
      final signatures = signaturesSnapshot.docs
          .map((doc) => SignatureModel.fromJson(doc.data()))
          .toList();

      // Group signatures by mandob, date, and inferred client
      final groupedSignatures = _groupSignaturesByMandobAndDate(signatures);

      int plansCreated = 0;
      int attendancesAdded = 0;

      // Create plans for each group
      for (final group in groupedSignatures) {
        final planId = _firestore.collection(_plansCollection).doc().id;
        
        // Convert signatures to attendance maps (excluding biometric data)
        final attendances = group.signatures
            .map((signature) => _signatureToAttendanceMap(signature))
            .toList();

        final plan = PlanModel(
          id: planId,
          date: group.date,
          mandobId: group.mandobId,
          clientId: group.clientId,
          clientName: group.clientName,
          startTime: _getEarliestTime(group.signatures),
          endTime: _getLatestTime(group.signatures),
          type: 'Lead', // Default type, can be updated later
          note: 'Migrated from signatures',
          status: 'attended',
          attendances: attendances,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        if (!dryRun) {
          await _firestore
              .collection(_plansCollection)
              .doc(planId)
              .set(plan.toJson());
        }

        plansCreated++;
        attendancesAdded += attendances.length;

        Log.i('${dryRun ? '[DRY RUN] ' : ''}Created plan for ${group.clientName} on ${group.date} with ${attendances.length} attendances');
      }

      Log.i('Migration completed successfully!');
      Log.i('Plans created: $plansCreated');
      Log.i('Attendances added: $attendancesAdded');

      if (!dryRun) {
        // Update migration status
        await _firestore
            .collection('migration_status')
            .doc('signatures_to_plans')
            .set({
          'completed': true,
          'completedAt': DateTime.now().millisecondsSinceEpoch,
          'plansCreated': plansCreated,
          'attendancesAdded': attendancesAdded,
          'originalSignatures': signatures.length,
        });
      }

      return true;
    } catch (e) {
      Log.e('Migration failed: $e');
      return false;
    }
  }

  /// Creates a backup of all signatures before migration
  static Future<void> _createSignaturesBackup() async {
    final signaturesSnapshot = await _firestore
        .collection(_signaturesCollection)
        .get();

    final batch = _firestore.batch();
    
    for (final doc in signaturesSnapshot.docs) {
      final backupRef = _firestore
          .collection(_backupCollection)
          .doc(doc.id);
      
      batch.set(backupRef, {
        ...doc.data(),
        'backedUpAt': DateTime.now().millisecondsSinceEpoch,
      });
    }

    await batch.commit();
    Log.i('Backup created with ${signaturesSnapshot.docs.length} signatures');
  }

  /// Groups signatures by mandob and date, inferring client information
  static List<SignatureGroup> _groupSignaturesByMandobAndDate(
      List<SignatureModel> signatures) {
    final groups = <String, SignatureGroup>{};

    for (final signature in signatures) {
      // Create a key for grouping: mandob_id + date + place (as client identifier)
      final key = '${signature.uid}_${signature.date}_${signature.place}';
      
      if (groups.containsKey(key)) {
        groups[key]!.signatures.add(signature);
      } else {
        groups[key] = SignatureGroup(
          mandobId: signature.uid,
          date: signature.date,
          clientId: _generateClientIdFromPlace(signature.place),
          clientName: signature.place,
          signatures: [signature],
        );
      }
    }

    return groups.values.toList();
  }

  /// Converts SignatureModel to AttendanceMap, excluding biometric data
  static AttendanceMap _signatureToAttendanceMap(SignatureModel signature) {
    return AttendanceMap(
      id: signature.id,
      date: signature.date,
      uid: signature.uid,
      mandobName: signature.mandobName,
      mandobEmail: signature.mandobEmail,
      lat: signature.lat,
      long: signature.long,
      place: signature.place,
      timestamp: signature.timestamp?.millisecondsSinceEpoch ?? 
                 DateTime.now().millisecondsSinceEpoch,
      isFirstOfDay: signature.isFirstOfDay,
      signatureUrl: signature.signatureUrl,
      // Note: Biometric data is intentionally excluded
    );
  }

  /// Generates a client ID from place name
  static String _generateClientIdFromPlace(String place) {
    // Simple hash-based ID generation from place name
    return place.toLowerCase().replaceAll(RegExp(r'[^a-z0-9]'), '_');
  }

  /// Gets the earliest time from a list of signatures
  static String _getEarliestTime(List<SignatureModel> signatures) {
    if (signatures.isEmpty) return '09:00';
    
    final earliest = signatures.reduce((a, b) => 
        (a.timestamp?.isBefore(b.timestamp ?? DateTime.now()) ?? false) ? a : b);
    
    return earliest.timestamp?.formatTimeToString ?? '09:00';
  }

  /// Gets the latest time from a list of signatures
  static String _getLatestTime(List<SignatureModel> signatures) {
    if (signatures.isEmpty) return '10:00';
    
    final latest = signatures.reduce((a, b) => 
        (a.timestamp?.isAfter(b.timestamp ?? DateTime.now()) ?? false) ? a : b);
    
    final latestTime = latest.timestamp?.formatTimeToString ?? '10:00';
    
    // Add 1 hour to the latest time as end time
    final timeParts = latestTime.split(':');
    final hour = int.parse(timeParts[0]);
    final minute = int.parse(timeParts[1]);
    final endHour = (hour + 1) % 24;
    
    return '${endHour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
  }

  /// Checks if migration has already been completed
  static Future<bool> isMigrationCompleted() async {
    try {
      final doc = await _firestore
          .collection('migration_status')
          .doc('signatures_to_plans')
          .get();
      
      return doc.exists && (doc.data()?['completed'] ?? false);
    } catch (e) {
      return false;
    }
  }

  /// Rolls back migration by restoring from backup
  static Future<bool> rollbackMigration() async {
    try {
      Log.i('Starting migration rollback...');
      
      // Check if backup exists
      final backupSnapshot = await _firestore
          .collection(_backupCollection)
          .get();
      
      if (backupSnapshot.docs.isEmpty) {
        Log.e('No backup found for rollback');
        return false;
      }

      // Delete all plans
      final plansSnapshot = await _firestore
          .collection(_plansCollection)
          .get();
      
      final batch = _firestore.batch();
      
      for (final doc in plansSnapshot.docs) {
        batch.delete(doc.reference);
      }
      
      await batch.commit();
      Log.i('Deleted ${plansSnapshot.docs.length} plans');

      // Restore signatures from backup
      final restoreBatch = _firestore.batch();
      
      for (final doc in backupSnapshot.docs) {
        final data = Map<String, dynamic>.from(doc.data());
        data.remove('backedUpAt'); // Remove backup metadata
        
        final signatureRef = _firestore
            .collection(_signaturesCollection)
            .doc(doc.id);
        
        restoreBatch.set(signatureRef, data);
      }
      
      await restoreBatch.commit();
      Log.i('Restored ${backupSnapshot.docs.length} signatures');

      // Update migration status
      await _firestore
          .collection('migration_status')
          .doc('signatures_to_plans')
          .set({
        'completed': false,
        'rolledBackAt': DateTime.now().millisecondsSinceEpoch,
      });

      Log.i('Migration rollback completed successfully');
      return true;
    } catch (e) {
      Log.e('Rollback failed: $e');
      return false;
    }
  }
}

/// Helper class for grouping signatures
class SignatureGroup {
  final String mandobId;
  final String date;
  final String clientId;
  final String clientName;
  final List<SignatureModel> signatures;

  SignatureGroup({
    required this.mandobId,
    required this.date,
    required this.clientId,
    required this.clientName,
    required this.signatures,
  });
}
