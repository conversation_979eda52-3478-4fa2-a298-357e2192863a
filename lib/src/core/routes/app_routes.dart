import 'package:flutter/material.dart';
import 'package:opti_tickets/src/screens/auth/view/login/login.screen.dart';
import 'package:opti_tickets/src/screens/auth/view/register/register_screen.dart';
import 'package:opti_tickets/src/screens/home/<USER>/home_screen.dart';
import 'package:opti_tickets/src/screens/reports_screen/view/reports_screen.dart';
import 'package:opti_tickets/src/screens/splash/view/main_screen.dart';
import 'package:opti_tickets/src/screens/splash/view/splash_screen.dart';

class AppRoutes {
  static const String splash = '/';
  static const String login = '/login';
  static const String register = '/register';
  static const String home = '/home';
  static const String selected = '/selected';
  static const String reports = '/reports';

  static Map<String, WidgetBuilder> get routes => {
        splash: (context) => const SplashScreen(),
        login: (context) => const LoginScreen(),
        register: (context) => const RegisterScreen(),
        home: (context) => const HomeScreen(),
        selected: (context) => const SelectedScreen(),
        reports: (context) => const ReportsScreen(),
      };

  static Route<dynamic>? onGenerateRoute(RouteSettings settings) {
    switch (settings.name) {
      case splash:
        return MaterialPageRoute(builder: (context) => const SplashScreen());
      case login:
        return MaterialPageRoute(builder: (context) => const LoginScreen());
      case register:
        return MaterialPageRoute(builder: (context) => const RegisterScreen());
      case selected:
        return MaterialPageRoute(builder: (context) => const SelectedScreen());
      case reports:
        return MaterialPageRoute(builder: (context) => const ReportsScreen());
      default:
        return MaterialPageRoute(
          builder: (context) => Scaffold(
            appBar: AppBar(title: const Text('Page Not Found')),
            body: const Center(
              child: Text('404 - Page Not Found'),
            ),
          ),
        );
    }
  }
}
