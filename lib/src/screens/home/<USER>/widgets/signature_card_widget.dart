import 'package:flutter/material.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/screens/signatures/models/signature_model.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../core/theme/color_manager.dart';

class SignatureCardWidget extends StatelessWidget {
  final SignatureModel signature;

  const SignatureCardWidget({super.key, required this.signature});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
        border: signature.isFirstOfDay
            ? Border.all(color: Colors.green, width: 2)
            : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: signature.isFirstOfDay
                      ? Colors.green.shade100
                      : Colors.blue.shade100,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  signature.isFirstOfDay
                      ? context.tr.firstOfDay
                      : context.tr.regularSignature,
                  style: TextStyle(
                    color: signature.isFirstOfDay
                        ? Colors.green.shade700
                        : Colors.blue.shade700,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const Spacer(),
              Text(
                signature.formattedTime,
                style: context.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              const Icon(Icons.location_on,
                  color: ColorManager.primaryColor, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  signature.place,
                  style: context.textTheme.titleMedium,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Button(
            label: context.tr.showOnMap,
            onPressed: () {
              launchUrl(Uri.parse(
                'https://www.google.com/maps/search/?api=1&query=${signature.lat},${signature.long}',
              ));
            },
          ).sized(height: 40),
          // Row(
          //   children: [
          //     Icon(Icons.person, color: Colors.grey[600], size: 20),
          //     const SizedBox(width: 8),
          //     Expanded(
          //       child: Text(
          //         signature.mandobName,
          //         style: context.textTheme.bodyMedium?.copyWith(
          //           color: Colors.grey[600],
          //         ),
          //       ),
          //     ),
          //   ],
          // ),
          // const SizedBox(height: 8),
          // Row(
          //   children: [
          //     Icon(Icons.gps_fixed, color: Colors.grey[600], size: 20),
          //     const SizedBox(width: 8),
          //     Expanded(
          //       child: Text(
          //         '${signature.lat.toStringAsFixed(6)}, ${signature.long.toStringAsFixed(6)}',
          //         style: context.textTheme.bodySmall?.copyWith(
          //           color: Colors.grey[600],
          //         ),
          //       ),
          //     ),
          //   ],
          // ),
        ],
      ),
    );
  }
}
