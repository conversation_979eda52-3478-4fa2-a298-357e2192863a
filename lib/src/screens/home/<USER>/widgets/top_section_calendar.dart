import 'package:calendar_timeline/calendar_timeline.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../core/shared/services/app_settings/controller/settings_controller.dart';
import '../../../../core/theme/color_manager.dart';

class CalendarWidget extends ConsumerWidget {
  final ValueNotifier<DateTime> selectedDayValue;

  const CalendarWidget({
    super.key,
    required this.selectedDayValue,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settingsController = ref.watch(settingsControllerProvider);

    return ValueListenableBuilder<DateTime>(
      valueListenable: selectedDayValue,
      builder: (context, selectedDay, _) {
        return Directionality(
          textDirection: TextDirection.ltr,
          child: CalendarTimeline(
            initialDate: selectedDay,
            firstDate: DateTime.now().subtract(const Duration(days: 365)),
            lastDate: DateTime.now().add(const Duration(days: 365)),
            onDateSelected: (date) {
              selectedDayValue.value = date;
            },
            leftMargin: AppSpaces.padding12,
            monthColor: Colors.blueGrey.shade300,
            dayColor: Colors.blueGrey.shade300,
            activeDayColor: Colors.white,
            activeBackgroundDayColor: ColorManager.primaryColor,
            dotsColor: const Color(0xFF333A47),
            showYears: false,
            // selectableDayPredicate: (date) => date.day != 23,

            locale: settingsController.isEnglish ? 'en_ISO' : 'ar',
            // 'en_ISO',
          ),
        );
      },
    );
  }
}
