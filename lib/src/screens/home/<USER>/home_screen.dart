import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/auth/models/user_model.dart';
import 'package:opti_tickets/src/screens/home/<USER>/widgets/signature_card_widget.dart';
import 'package:opti_tickets/src/screens/home/<USER>/widgets/top_section_calendar.dart';
import 'package:opti_tickets/src/screens/signatures/providers/signature_providers.dart';
import 'package:opti_tickets/src/screens/signatures/view/add_plan_sheet.dart';
import 'package:opti_tickets/src/screens/signatures/view/add_signature_sheet.dart';
import 'package:opti_tickets/src/screens/signatures/view/clients_screen.dart';
import 'package:opti_tickets/src/screens/signatures/view/widgets/plan_card_widget.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../core/shared/widgets/change_language/change_language.widget.dart';
import '../../main_screen/view/widgets/home_app_bar.dart';

class HomeScreen extends HookConsumerWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedDate = useState(DateTime.now());
    final selectedUser = useState<UserModel?>(null);
    final viewMode = useState<String>('signatures'); // 'signatures' or 'plans'

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(signatureControllerProvider).initialize();
        ref.read(planControllerProvider).initialize();
      });
      return () {};
    }, []);

    void showAddSignatureSheet() {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) => const AddSignatureSheet(),
      ).then((result) {
        if (result == true) {
          // Refresh signatures after adding
          ref.read(signatureControllerProvider).getTodaySignatures();
        }
      });
    }

    void showAddPlanSheet() {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) =>
            AddPlanSheet(selectedDate: selectedDate.value.formatDateToString),
      ).then((result) {
        if (result == true) {
          // Refresh handled by stream
        }
      });
    }

    void onDateSelected(DateTime date) {
      selectedDate.value = date;
      ref.read(signatureControllerProvider).setSelectedDate(date);
    }

    void onUserSelected(UserModel? user) {
      selectedUser.value = user;
      ref.read(signatureControllerProvider).setSelectedUser(user);
    }

    final currentUser = UserModel.currentUser();
    final signatureController = ref.watch(signatureControllerProvider);

    // Stream-based signatures with real-time updates
    final params = (selectedDate.value, selectedUser.value?.uid);
    final signaturesStream =
        ref.watch(getSignaturesStreamControllerProvider(params));

    return Scaffold(
      backgroundColor: ColorManager.lightGreyBackground,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(70),
        child: AppBar(
          automaticallyImplyLeading: false,
          title: Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Text(context.tr.signatures,
                style: context.textTheme.titleLarge?.copyWith(
                  color: ColorManager.white,
                  fontSize: 20,
                )),
          ),
          backgroundColor: ColorManager.primaryColor,
          foregroundColor: Colors.white,
          actions: [
            // Clients button
            IconButton(
              onPressed: () => const ClientsScreen().navigate(),
              icon: const Icon(
                Icons.people,
                color: ColorManager.secondaryColor,
              ),
            ).decorated(
              width: 45,
              color: ColorManager.white,
              radius: BorderRadius.circular(AppRadius.radius8),
              margin: const EdgeInsets.only(
                top: AppSpaces.screenPadding,
              ),
            ),
            IconButton(
                onPressed: () => showDialog(
                    context: context,
                    builder: (_) => const ChangeLanguageWidget()),
                icon: const Icon(
                  Icons.language,
                  color: ColorManager.secondaryColor,
                )).decorated(
              width: 45,
              color: ColorManager.white,
              radius: BorderRadius.circular(AppRadius.radius8),
              margin: const EdgeInsets.only(
                top: AppSpaces.screenPadding,
                left: AppSpaces.screenPadding,
              ),
            ),
            IconButton(
              onPressed: () => showDialog(
                  context: context, builder: (_) => const LogoutDialog()),
              icon: const Icon(
                Icons.logout,
                color: ColorManager.errorColor,
              ),
            ).decorated(
              width: 45,
              color: ColorManager.white,
              radius: BorderRadius.circular(AppRadius.radius8),
              margin: const EdgeInsets.only(
                top: AppSpaces.screenPadding,
                right: AppSpaces.screenPadding,
                left: AppSpaces.screenPadding,
              ),
            )
          ],
        ),
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          await ref.read(signatureControllerProvider).getTodaySignatures();
        },
        child: CustomScrollView(
          slivers: [
            // Date picker section
            SliverToBoxAdapter(
                child: CalendarWidget(
              selectedDayValue: selectedDate,
            )),

            // View mode toggle
            SliverToBoxAdapter(
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Row(
                  children: [
                    Expanded(
                      child: SegmentedButton<String>(
                        segments: [
                          ButtonSegment<String>(
                            value: 'signatures',
                            label: Text(context.tr.signatures),
                            icon: const Icon(Icons.edit, size: 16),
                          ),
                          ButtonSegment<String>(
                            value: 'plans',
                            label: Text(context.tr.plans),
                            icon: const Icon(Icons.schedule, size: 16),
                          ),
                        ],
                        selected: {viewMode.value},
                        onSelectionChanged: (Set<String> newSelection) {
                          viewMode.value = newSelection.first;
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Admin user selector
            if (currentUser.isAdmin) ...[
              SliverToBoxAdapter(
                child: Container(
                  margin: const EdgeInsets.only(left: 16, right: 16, top: 16),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.1),
                        spreadRadius: 1,
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        context.tr.selectDelivery,
                        style: context.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      DropdownButtonFormField<UserModel>(
                        value: selectedUser.value,
                        decoration: InputDecoration(
                          hintText: context.tr.selectDelivery,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        items: signatureController.users
                            .map(
                              (user) => DropdownMenuItem<UserModel>(
                                value: user,
                                child: Text(user.name),
                              ),
                            )
                            .toList(),
                        onChanged: onUserSelected,
                      ),
                    ],
                  ),
                ),
              ),
              const SliverToBoxAdapter(child: SizedBox(height: 16)),
            ],

            // Content based on view mode
            if (viewMode.value == 'signatures')
              // Signatures list
              signaturesStream.when(
                data: (signatures) => signatures.isEmpty
                    ? SliverToBoxAdapter(
                        child: Container(
                          margin: const EdgeInsets.all(16),
                          padding: const EdgeInsets.all(32),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Column(
                            children: [
                              Icon(
                                Icons.assignment_outlined,
                                size: 64,
                                color: Colors.grey[400],
                              ),
                              const SizedBox(height: 16),
                              Text(
                                context.tr.noSignaturesToday,
                                style: context.textTheme.titleMedium?.copyWith(
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        ),
                      )
                    : SliverList(
                        delegate: SliverChildBuilderDelegate(
                          (context, index) {
                            final signature = signatures[index];

                            return SignatureCardWidget(
                              signature: signature,
                            );
                          },
                          childCount: signatures.length,
                        ),
                      ),
                loading: () => const SliverToBoxAdapter(
                  child: Center(child: CircularProgressIndicator()),
                ),
                error: (error, stack) => const SliverToBoxAdapter(
                  child: SizedBox(),
                ),
              )
            else
              // Plans timeline
              Consumer(
                builder: (context, ref, child) {
                  final dateString = selectedDate.value.formatDateToString;
                  final targetUid = selectedUser.value?.uid ?? currentUser.uid;

                  final plansStream =
                      currentUser.isAdmin && selectedUser.value != null
                          ? ref.watch(plansByDateAndMandobStreamProvider(
                              (dateString, targetUid)))
                          : ref.watch(myPlansForDateStreamProvider(dateString));

                  return plansStream.when(
                    data: (plans) => plans.isEmpty
                        ? SliverToBoxAdapter(
                            child: Container(
                              margin: const EdgeInsets.all(16),
                              padding: const EdgeInsets.all(32),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Column(
                                children: [
                                  Icon(
                                    Icons.schedule_outlined,
                                    size: 64,
                                    color: Colors.grey[400],
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    context.tr.noPlansTodayFound,
                                    style:
                                        context.textTheme.titleMedium?.copyWith(
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          )
                        : SliverList(
                            delegate: SliverChildBuilderDelegate(
                              (context, index) {
                                final plan = plans[index];
                                return PlanCardWidget(plan: plan);
                              },
                              childCount: plans.length,
                            ),
                          ),
                    loading: () => const SliverToBoxAdapter(
                      child: Center(child: CircularProgressIndicator()),
                    ),
                    error: (error, stack) => const SliverToBoxAdapter(
                      child: SizedBox(),
                    ),
                  );
                },
              ),
          ],
        ),
      ),
      floatingActionButton: !currentUser.isAdmin
          ? Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (viewMode.value == 'plans')
                  FloatingActionButton(
                    onPressed: showAddPlanSheet,
                    heroTag: "add_plan",
                    backgroundColor: Colors.green,
                    child: const Icon(Icons.schedule),
                  ),
                if (viewMode.value == 'plans') const SizedBox(height: 16),
                FloatingActionButton.extended(
                  onPressed: showAddSignatureSheet,
                  heroTag: "add_signature",
                  icon: const Icon(Icons.add),
                  label: Text(context.tr.addSignature),
                  backgroundColor: ColorManager.primaryColor,
                ),
              ],
            )
          : null,
    );
  }
}
