import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:open_filex/open_filex.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/auth/models/user_model.dart';
import 'package:opti_tickets/src/screens/home/<USER>/widgets/signature_card_widget.dart';
import 'package:opti_tickets/src/screens/reports_screen/services/excel_export_service.dart';
import 'package:opti_tickets/src/screens/reports_screen/services/pdf_export_service.dart';
import 'package:opti_tickets/src/screens/signatures/models/signature_model.dart';
import 'package:opti_tickets/src/screens/signatures/providers/signature_providers.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:xr_helper/xr_helper.dart';

class ReportsScreen extends HookConsumerWidget {
  const ReportsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final startDate =
        useState(DateTime.now().subtract(const Duration(days: 30)));
    final endDate = useState(DateTime.now());
    final selectedUser = useState<UserModel?>(null);

    // Stream-based report data with real-time updates
    final reportParams =
        (startDate.value, endDate.value, selectedUser.value?.uid);

    final reportStream =
        ref.watch(getSignaturesByDateRangeStreamProvider(reportParams));

    Future<void> loadUsers() async {
      await ref.read(signatureControllerProvider).getAllUsers();
    }

    Widget buildSummaryItem(String label, String value, IconData icon) {
      return Column(
        children: [
          Icon(icon, color: ColorManager.primaryColor),
          const SizedBox(height: 4),
          Text(
            value,
            style: context.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: ColorManager.primaryColor,
            ),
          ),
          Text(
            label,
            style: context.textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      );
    }

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        loadUsers();
      });
      return null;
    }, []);

    Future<void> selectDateRange() async {
      final DateTimeRange? picked = await showDateRangePicker(
        context: context,
        firstDate: DateTime.now().subtract(const Duration(days: 365)),
        lastDate: DateTime.now(),
        initialDateRange:
            DateTimeRange(start: startDate.value, end: endDate.value),
      );

      if (picked != null) {
        startDate.value = picked.start;
        endDate.value = picked.end;
      }
    }

    void onUserSelected(UserModel? user) {
      selectedUser.value = user;
    }

    final signatureController = ref.watch(signatureControllerProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(context.tr.reports,
            style: context.textTheme.titleLarge?.copyWith(
              color: Colors.white,
              fontSize: 20,
            )),
        backgroundColor: ColorManager.primaryColor,
        foregroundColor: Colors.white,
        actions: reportStream.when(
          data: (data) => data.isNotEmpty
              ? [
                  // PDF Export Button
                  IconButton(
                    icon: const Icon(
                      Icons.picture_as_pdf,
                      color: Colors.white,
                    ),
                    onPressed: () => exportToPDF(
                      context,
                      data,
                      startDate.value,
                      endDate.value,
                      selectedUser.value,
                    ),
                    tooltip: 'Export to PDF',
                  ),
                  // Excel Export Button
                  IconButton(
                    icon: const Icon(
                      Icons.table_chart,
                      color: Colors.white,
                    ),
                    onPressed: () => exportToExcel(
                      data,
                      startDate.value,
                      endDate.value,
                      selectedUser.value,
                    ),
                    tooltip: 'Export to Excel',
                  ),
                ]
              : [],
          loading: () => [],
          error: (_, __) => [],
        ),
      ),
      body: Column(
        children: [
          // Filters section
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey[50],
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Date range selector
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            context.tr.dateRange,
                            style: context.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          InkWell(
                            onTap: selectDateRange,
                            child: Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey[300]!),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                children: [
                                  const Icon(Icons.date_range),
                                  const SizedBox(width: 8),
                                  Text(
                                    '${startDate.value.formatDateToString} - ${endDate.value.formatDateToString}',
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // User selector
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      context.tr.selectDelivery,
                      style: context.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<UserModel>(
                      value: selectedUser.value,
                      decoration: InputDecoration(
                        hintText: context.tr.selectDelivery,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      items: signatureController.users
                          .map(
                            (user) => DropdownMenuItem<UserModel>(
                              value: user,
                              child: Text(user.name),
                            ),
                          )
                          .toList(),
                      onChanged: onUserSelected,
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Summary section
          Expanded(
            child: reportStream.when(
              data: (data) => data.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.assessment_outlined,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            context.tr.noDataAvailable,
                            style: context.textTheme.titleMedium?.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    )
                  : ListView(
                      shrinkWrap: true,
                      children: [
                        Container(
                          margin: const EdgeInsets.all(16),
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.grey.withOpacity(0.2),
                                spreadRadius: 1,
                                blurRadius: 4,
                                offset: const Offset(0, -2),
                              ),
                            ],
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              buildSummaryItem(
                                context.tr.totalSignatures,
                                data.length.toString(),
                                Icons.assignment,
                              ),
                              buildSummaryItem(
                                context.tr.officeSignatures,
                                data
                                    .where((s) => s.isFirstOfDay)
                                    .length
                                    .toString(),
                                Icons.wb_sunny,
                              ),
                              buildSummaryItem(
                                context.tr.regularSignatures,
                                data
                                    .where((s) => !s.isFirstOfDay)
                                    .length
                                    .toString(),
                                Icons.access_time,
                              ),
                            ],
                          ),
                        ),
                        ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          padding: const EdgeInsets.all(16),
                          itemCount: data.length,
                          itemBuilder: (context, index) {
                            final signature = data[index];

                            return SignatureCardWidget(signature: signature);
                          },
                        ),
                      ],
                    ),
              loading: () => const SizedBox.shrink(),
              error: (_, __) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.assessment_outlined,
                      size: 64,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      context.tr.noDataAvailable,
                      style: context.textTheme.titleMedium?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> exportToPDF(
    BuildContext context,
    List<SignatureModel> signatures,
    DateTime startDate,
    DateTime endDate,
    UserModel? selectedUser,
  ) async {
    try {
      if (signatures.isEmpty) {
        Fluttertoast.showToast(
            msg: 'No data to export', backgroundColor: Colors.red);
        return;
      }

      Fluttertoast.showToast(msg: 'Generating PDF file...');

      // Generate PDF
      final pw.Document generatedPDF =
          await PdfExportService.generateSignatureReportPDF(
        context,
        signatures: signatures,
        fromDate: startDate,
        toDate: endDate,
        selectedUser: selectedUser,
      );

      // Save PDF
      final String generatedPDFReportPath = await PdfExportService.savePDF(
        generatedPDF,
        title: selectedUser != null
            ? 'signature_report_${selectedUser.name}'
            : 'signature_report',
      );

      Fluttertoast.showToast(
          msg: 'PDF file generated successfully',
          backgroundColor: Colors.green);

      // Open the file
      await OpenFilex.open(generatedPDFReportPath);
    } catch (e) {
      Fluttertoast.showToast(
          msg: 'Failed to export PDF: ${e.toString()}',
          backgroundColor: Colors.red);
    }
  }

  Future<void> exportToExcel(
    List<SignatureModel> signatures,
    DateTime startDate,
    DateTime endDate,
    UserModel? selectedUser,
  ) async {
    try {
      if (signatures.isEmpty) {
        Fluttertoast.showToast(
            msg: 'No data to export', backgroundColor: Colors.red);
        return;
      }

      Fluttertoast.showToast(msg: 'Generating Excel file...');

      final String filePath =
          await ExcelExportService.generateSignatureReportExcel(
        signatures: signatures,
        fromDate: startDate,
        toDate: endDate,
        isEnglish: true,
        // You can make this dynamic based on app locale
        selectedUser: selectedUser,
      );

      Fluttertoast.showToast(
          msg: 'Excel file generated successfully',
          backgroundColor: Colors.green);

      // Open the file
      await OpenFilex.open(filePath);
    } catch (e) {
      Fluttertoast.showToast(
          msg: 'Failed to export Excel: ${e.toString()}',
          backgroundColor: Colors.red);
    }
  }
}
