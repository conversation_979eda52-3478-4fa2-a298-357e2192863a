import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/screens/auth/models/user_model.dart';
import 'package:opti_tickets/src/screens/signatures/models/signature_model.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/src/widgets/font.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:xr_helper/xr_helper.dart';

class PdfExportService {
  //? Base text widget for proper Arabic/English formatting
  static pw.Widget baseText(
    String text, {
    required Font arabicFont,
    bool isBold = false,
    bool isLtr = false,
    PdfColor? color,
  }) {
    return pw.Padding(
      padding: const pw.EdgeInsets.all(2),
      child: pw.Text(
        text,
        textScaleFactor: 1,
        textAlign: pw.TextAlign.center,
        textDirection: pw.TextDirection.rtl,
        style: pw.TextStyle(
          font: arabicFont,
          fontWeight: isBold ? pw.FontWeight.bold : pw.FontWeight.normal,
          color: color,
        ),
      ),
    );
  }

  //? Save PDF to device
  static Future<String> savePDF(pw.Document pdf,
      {String title = 'report'}) async {
    final output = await getTemporaryDirectory();
    final pdfFile = File("${output.path}/$title.pdf");
    await pdfFile.writeAsBytes(await pdf.save());
    return pdfFile.path;
  }

  //? Generate signature report PDF
  static Future<pw.Document> generateSignatureReportPDF(
    BuildContext appContext, {
    required List<SignatureModel> signatures,
    required DateTime fromDate,
    required DateTime toDate,
    UserModel? selectedUser,
  }) async {
    final arabicFont = Font.ttf(
      await rootBundle.load("assets/fonts/cairo/Cairo-Bold.ttf"),
    );

    final pdf = pw.Document();
    final isEnglish = appContext.isEnglish;

    // English table layout
    pw.Widget englishTable() {
      return pw.Table(
        columnWidths: {
          0: const pw.FlexColumnWidth(2), // Employee Name
          1: const pw.FlexColumnWidth(2), // Date
          2: const pw.FlexColumnWidth(1.5), // Time
          3: const pw.FlexColumnWidth(2), // Place
          4: const pw.FlexColumnWidth(2), // Type
          // 5: const pw.FlexColumnWidth(2), // Location
        },
        border: pw.TableBorder.all(),
        children: [
          pw.TableRow(
            children: [
              baseText(
                'Employee Name',
                arabicFont: arabicFont,
                isBold: true,
              ),
              baseText(
                'Date',
                arabicFont: arabicFont,
                isBold: true,
              ),
              baseText(
                'Time',
                arabicFont: arabicFont,
                isBold: true,
              ),
              baseText(
                'Place',
                arabicFont: arabicFont,
                isBold: true,
              ),
              baseText(
                'Type',
                arabicFont: arabicFont,
                isBold: true,
              ),
              // baseText(
              //   'Location',
              //   arabicFont: arabicFont,
              //   isBold: true,
              // ),
            ],
          ),
          ...signatures.map(
            (signature) => pw.TableRow(
              children: [
                baseText(
                  signature.mandobName,
                  arabicFont: arabicFont,
                ),
                baseText(
                  signature.formattedDate,
                  arabicFont: arabicFont,
                  isLtr: true,
                ),
                baseText(
                  signature.formattedTime,
                  arabicFont: arabicFont,
                  isLtr: true,
                ),
                baseText(
                  signature.place,
                  arabicFont: arabicFont,
                ),
                baseText(
                  signature.isFirstOfDay ? 'First of Day' : 'Regular',
                  arabicFont: arabicFont,
                ),
                // baseText(
                //   '${signature.lat.toStringAsFixed(6)}, ${signature.long.toStringAsFixed(6)}',
                //   arabicFont: arabicFont,
                //   isLtr: true,
                // ),
              ],
            ),
          ),
        ],
      );
    }

    // Arabic table layout (reversed order)
    pw.Widget arabicTable() {
      return pw.Table(
        columnWidths: {
          // 0: const pw.FlexColumnWidth(2), // Location
          0: const pw.FlexColumnWidth(2), // Type
          1: const pw.FlexColumnWidth(2), // Place
          2: const pw.FlexColumnWidth(1.5), // Time
          3: const pw.FlexColumnWidth(2), // Date
          4: const pw.FlexColumnWidth(2), // Employee Name
        },
        border: pw.TableBorder.all(),
        children: [
          pw.TableRow(
            children: [
              // baseText(
              //   'الموقع',
              //   arabicFont: arabicFont,
              //   isBold: true,
              // ),
              baseText(
                'النوع',
                arabicFont: arabicFont,
                isBold: true,
              ),
              baseText(
                'المكان',
                arabicFont: arabicFont,
                isBold: true,
              ),
              baseText(
                'الوقت',
                arabicFont: arabicFont,
                isBold: true,
              ),
              baseText(
                'التاريخ',
                arabicFont: arabicFont,
                isBold: true,
              ),
              baseText(
                'اسم الموظف',
                arabicFont: arabicFont,
                isBold: true,
              ),
            ],
          ),
          ...signatures.map(
            (signature) => pw.TableRow(
              children: [
                // baseText(
                //   '${signature.lat.toStringAsFixed(6)}, ${signature.long.toStringAsFixed(6)}',
                //   arabicFont: arabicFont,
                //   isLtr: true,
                // ),
                baseText(
                  signature.isFirstOfDay ? 'أول توقيع' : 'توقيع عادى',
                  arabicFont: arabicFont,
                ),
                baseText(
                  signature.place,
                  arabicFont: arabicFont,
                ),
                baseText(
                  signature.formattedTime,
                  arabicFont: arabicFont,
                  isLtr: true,
                ),
                baseText(
                  signature.formattedDate,
                  arabicFont: arabicFont,
                  isLtr: true,
                ),
                baseText(
                  signature.mandobName,
                  arabicFont: arabicFont,
                ),
              ],
            ),
          ),
        ],
      );
    }

    // Create the PDF page
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return <pw.Widget>[
            pw.Header(
              level: 0,
              child: pw.Text(
                selectedUser != null
                    ? '${selectedUser.name} (${fromDate.formatDateToString} - ${toDate.formatDateToString})'
                    : 'Signature Report (${fromDate.formatDateToString} - ${toDate.formatDateToString})',
                textScaleFactor: 2,
                textAlign: pw.TextAlign.center,
                textDirection: pw.TextDirection.rtl,
                style: pw.TextStyle(
                  font: arabicFont,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
            ),
            pw.SizedBox(height: 20),
            if (isEnglish) englishTable() else arabicTable(),
          ];
        },
      ),
    );

    return pdf;
  }
}
