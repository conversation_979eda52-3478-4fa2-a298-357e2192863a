import 'dart:io';

import 'package:opti_tickets/src/screens/auth/models/user_model.dart';
import 'package:opti_tickets/src/screens/signatures/models/signature_model.dart';
import 'package:path_provider/path_provider.dart';
import 'package:syncfusion_flutter_xlsio/xlsio.dart';
import 'package:xr_helper/xr_helper.dart';

class ExcelExportService {
  static Future<String> generateSignatureReportExcel({
    required List<SignatureModel> signatures,
    required DateTime fromDate,
    required DateTime toDate,
    required bool isEnglish,
    UserModel? selectedUser,
  }) async {
    final Workbook workbook = Workbook();
    final Worksheet sheet = workbook.worksheets[0];
    final Style headerStyle = workbook.styles.add('HeaderStyle');
    final Style dataStyle = workbook.styles.add('DataStyle');

    // Define styles
    headerStyle.hAlign = HAlignType.center;
    headerStyle.bold = true;
    headerStyle.backColor = '#E3F2FD';
    dataStyle.hAlign = HAlignType.center;

    if (isEnglish) {
      // Define headers for English table
      final Range header1 = sheet.getRangeByName('A1');
      header1.setText('Employee Name');
      header1.cellStyle = headerStyle;

      final Range header2 = sheet.getRangeByName('B1');
      header2.setText('Date');
      header2.cellStyle = headerStyle;

      final Range header3 = sheet.getRangeByName('C1');
      header3.setText('Time');
      header3.cellStyle = headerStyle;

      final Range header4 = sheet.getRangeByName('D1');
      header4.setText('Place');
      header4.cellStyle = headerStyle;

      final Range header5 = sheet.getRangeByName('E1');
      header5.setText('Type');
      header5.cellStyle = headerStyle;

      // final Range header6 = sheet.getRangeByName('F1');
      // header6.setText('Location');
      // header6.cellStyle = headerStyle;

      int rowIndex = 2;
      for (final signature in signatures) {
        final Range employeeCell = sheet.getRangeByIndex(rowIndex, 1);
        employeeCell.setText(signature.mandobName);
        employeeCell.cellStyle = dataStyle;

        final Range dateCell = sheet.getRangeByIndex(rowIndex, 2);
        dateCell.setText(signature.formattedDate);
        dateCell.cellStyle = dataStyle;

        final Range timeCell = sheet.getRangeByIndex(rowIndex, 3);
        timeCell.setText(signature.formattedTime);
        timeCell.cellStyle = dataStyle;

        final Range placeCell = sheet.getRangeByIndex(rowIndex, 4);
        placeCell.setText(signature.place);
        placeCell.cellStyle = dataStyle;

        final Range typeCell = sheet.getRangeByIndex(rowIndex, 5);
        typeCell
            .setText(signature.isFirstOfDay ? 'Office Signature' : 'Regular');
        typeCell.cellStyle = dataStyle;

        // final Range locationCell = sheet.getRangeByIndex(rowIndex, 6);
        // locationCell.setText('${signature.lat.toStringAsFixed(6)}, ${signature.long.toStringAsFixed(6)}');
        // locationCell.cellStyle = dataStyle;

        rowIndex++;
      }
    } else {
      // Define headers for Arabic table
      final Range header1 = sheet.getRangeByName('A1');
      header1.setText('اسم الموظف');
      header1.cellStyle = headerStyle;

      final Range header2 = sheet.getRangeByName('B1');
      header2.setText('التاريخ');
      header2.cellStyle = headerStyle;

      final Range header3 = sheet.getRangeByName('C1');
      header3.setText('الوقت');
      header3.cellStyle = headerStyle;

      final Range header4 = sheet.getRangeByName('D1');
      header4.setText('المكان');
      header4.cellStyle = headerStyle;

      final Range header5 = sheet.getRangeByName('E1');
      header5.setText('النوع');
      header5.cellStyle = headerStyle;

      // final Range header6 = sheet.getRangeByName('F1');
      // header6.setText('الموقع');
      // header6.cellStyle = headerStyle;

      int rowIndex = 2;
      for (final signature in signatures) {
        final Range employeeCell = sheet.getRangeByIndex(rowIndex, 1);
        employeeCell.setText(signature.mandobName);
        employeeCell.cellStyle = dataStyle;

        final Range dateCell = sheet.getRangeByIndex(rowIndex, 2);
        dateCell.setText(signature.formattedDate);
        dateCell.cellStyle = dataStyle;

        final Range timeCell = sheet.getRangeByIndex(rowIndex, 3);
        timeCell.setText(signature.formattedTime);
        timeCell.cellStyle = dataStyle;

        final Range placeCell = sheet.getRangeByIndex(rowIndex, 4);
        placeCell.setText(signature.place);
        placeCell.cellStyle = dataStyle;

        final Range typeCell = sheet.getRangeByIndex(rowIndex, 5);
        typeCell
            .setText(signature.isFirstOfDay ? 'توقيع المكتب' : 'توقيع عادي');
        typeCell.cellStyle = dataStyle;

        // final Range locationCell = sheet.getRangeByIndex(rowIndex, 6);
        // locationCell.setText('${signature.lat.toStringAsFixed(6)}, ${signature.long.toStringAsFixed(6)}');
        // locationCell.cellStyle = dataStyle;

        rowIndex++;
      }
    }

    // Auto-fit columns
    sheet.autoFitColumn(1);
    sheet.autoFitColumn(2);
    sheet.autoFitColumn(3);
    sheet.autoFitColumn(4);
    sheet.autoFitColumn(5);
    // sheet.autoFitColumn(6);

    // Save the Excel file
    final Directory appDocDir = await getApplicationDocumentsDirectory();
    final String appDocPath = appDocDir.path;
    final String fileName = selectedUser != null
        ? 'SignatureReport_${selectedUser.name}_${DateTime.now().toIso8601String()}.xlsx'
        : 'SignatureReport_${DateTime.now().toIso8601String()}.xlsx';
    final String path = '$appDocPath/$fileName';

    final List<int> bytes = workbook.saveAsStream();
    workbook.dispose();

    final File file = File(path);
    await file.writeAsBytes(bytes);

    Log.w('Signature report Excel sheet saved at $path');

    return path;
  }
}
