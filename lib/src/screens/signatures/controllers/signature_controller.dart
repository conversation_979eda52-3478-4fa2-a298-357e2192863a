import 'package:opti_tickets/src/screens/auth/models/user_model.dart';
import 'package:opti_tickets/src/screens/signatures/models/signature_model.dart';
import 'package:opti_tickets/src/screens/signatures/repositories/signature_repository.dart';
import 'package:xr_helper/xr_helper.dart';

class SignatureController extends BaseVM {
  final SignatureRepository signatureRepo;

  SignatureController({
    required this.signatureRepo,
  });

  // * State variables ================================
  List<SignatureModel> _signatures = [];
  List<SignatureModel> get signatures => _signatures;

  List<UserModel> _users = [];
  List<UserModel> get users => _users;

  DateTime _selectedDate = DateTime.now();
  DateTime get selectedDate => _selectedDate;

  UserModel? _selectedUser;
  UserModel? get selectedUser => _selectedUser;

  bool _canAddFirstSignature = false;
  bool get canAddFirstSignature => _canAddFirstSignature;

  // * Add signature ================================
  Future<bool> addSignature({
    required String place,
    bool requireAuth = true,
  }) async {
    return await baseFunction(
      () async {
        final result = await signatureRepo.addSignature(
          place: place,
          requireAuth: requireAuth,
        );

        if (result) {
          // Refresh today's signatures
          await getTodaySignatures();
          await checkCanAddFirstSignature();
        }

        return result;
      },
    );
  }

  // * Get signatures by date ================================
  Future<void> getSignaturesByDate({
    required DateTime date,
    String? uid,
  }) async {
    await baseFunction(
      () async {
        _selectedDate = date;
        final dateString = date.formatDateToString;

        _signatures = await signatureRepo.getSignaturesByDate(
          date: dateString,
          uid: uid,
        );

        notifyListeners();
      },
    );
  }

  // * Get today's signatures ================================
  Future<void> getTodaySignatures() async {
    await baseFunction(
      () async {
        _signatures = await signatureRepo.getTodaySignatures();
      },
    );
  }

  // * Get signatures by date range (for reports) ================================
  Future<List<SignatureModel>> getSignaturesByDateRange({
    required DateTime startDate,
    required DateTime endDate,
    String? uid,
  }) async {
    return await baseFunction(
      () async {
        return await signatureRepo.getSignaturesByDateRange(
          startDate: startDate.formatDateToString,
          endDate: endDate.formatDateToString,
          uid: uid,
        );
      },
    );
  }

  // * Get all users for admin dropdown ================================
  Future<void> getAllUsers() async {
    await baseFunction(
      () async {
        _users = await signatureRepo.getAllUsers();
        notifyListeners();
      },
    );
  }

  // * Set selected user (for admin filtering) ================================
  void setSelectedUser(UserModel? user) {
    _selectedUser = user;
    notifyListeners();

    // Refresh signatures for selected user
    if (user != null) {
      getSignaturesByDate(date: _selectedDate, uid: user.uid);
    } else {
      getSignaturesByDate(date: _selectedDate);
    }
  }

  // * Set selected date ================================
  void setSelectedDate(DateTime date) {
    _selectedDate = date;
    getSignaturesByDate(date: date, uid: _selectedUser?.uid);
    notifyListeners();
  }

  // * Check if user can add first signature ================================
  Future<void> checkCanAddFirstSignature() async {
    await baseFunction(
      () async {
        _canAddFirstSignature = await signatureRepo.canAddFirstSignature();
        notifyListeners();
      },
    );
  }

  // * Check permissions ================================
  Future<bool> checkLocationPermission() async {
    return await baseFunction(
      () async {
        return await signatureRepo.checkLocationPermission();
      },
    );
  }

  Future<bool> requestLocationPermission() async {
    return await baseFunction(
      () async {
        return await signatureRepo.requestLocationPermission();
      },
    );
  }

  Future<bool> checkBiometricAvailability() async {
    return await baseFunction(
      () async {
        return await signatureRepo.checkBiometricAvailability();
      },
    );
  }

  // * Helper methods ================================
  bool get hasSignaturesToday => _signatures.isNotEmpty;

  int get todaySignatureCount => _signatures.length;

  SignatureModel? get firstSignatureOfDay =>
      _signatures.where((s) => s.isFirstOfDay).firstOrNull;

  List<SignatureModel> get regularSignatures =>
      _signatures.where((s) => !s.isFirstOfDay).toList();

  // * Initialize controller ================================
  Future<void> initialize() async {
    await baseFunction(
      () async {
        final currentUser = UserModel.currentUser();
        if (currentUser.isAdmin) {
          await getAllUsers();
        } else {
          await getTodaySignatures();
          await checkCanAddFirstSignature();
        }
      },
    );
  }

  // * Clear data ================================
  void clearData() {
    _signatures.clear();
    _users.clear();
    _selectedUser = null;
    _selectedDate = DateTime.now();
    _canAddFirstSignature = false;
    notifyListeners();
  }

  // * Stream Methods ================================

  // Get signatures stream for specific date and user
  Stream<List<SignatureModel>> getSignaturesStream({
    required DateTime date,
    String? userId,
  }) {
    return signatureRepo.getSignaturesStream(
      date: date,
      userId: userId,
    );
  }

  // Get signatures stream for date range (admin use)
  Stream<List<SignatureModel>> getSignaturesByDateRangeStream({
    required DateTime startDate,
    required DateTime endDate,
    String? uid,
  }) {
    return signatureRepo.getSignaturesByDateRangeStream(
      startDate: startDate,
      endDate: endDate,
      uid: uid,
    );
  }
}
