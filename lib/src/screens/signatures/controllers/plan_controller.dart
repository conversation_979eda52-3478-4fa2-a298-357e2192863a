import 'package:opti_tickets/src/screens/auth/models/user_model.dart';
import 'package:opti_tickets/src/screens/signatures/models/plan_model.dart';
import 'package:opti_tickets/src/screens/signatures/repositories/plan_repository.dart';
import 'package:xr_helper/xr_helper.dart';

class PlanController extends BaseVM {
  final PlanRepository planRepo;

  PlanController({
    required this.planRepo,
  });

  // * State variables ================================
  List<PlanModel> _plans = [];
  PlanModel? _selectedPlan;
  String _selectedDate = DateTime.now().formatDateToString;

  // * Getters ================================
  List<PlanModel> get plans => _plans;
  PlanModel? get selectedPlan => _selectedPlan;
  String get selectedDate => _selectedDate;

  // * Initialize ================================
  Future<void> initialize() async {
    await getMyPlansForToday();
  }

  // * Add plan ================================
  Future<bool> addPlan({
    required String date,
    required String clientId,
    required String clientName,
    required String startTime,
    required String endTime,
    required String type,
    String note = '',
  }) async {
    return await baseFunction(
      () async {
        final result = await planRepo.addPlan(
          date: date,
          clientId: clientId,
          clientName: clientName,
          startTime: startTime,
          endTime: endTime,
          type: type,
          note: note,
        );

        if (result) {
          await getMyPlansForDate(date);
        }

        return result;
      },
    );
  }

  // * Update plan ================================
  Future<bool> updatePlan({
    required String planId,
    required String date,
    required String clientId,
    required String clientName,
    required String startTime,
    required String endTime,
    required String type,
    String note = '',
  }) async {
    return await baseFunction(
      () async {
        final result = await planRepo.updatePlan(
          planId: planId,
          date: date,
          clientId: clientId,
          clientName: clientName,
          startTime: startTime,
          endTime: endTime,
          type: type,
          note: note,
        );

        if (result) {
          await getMyPlansForDate(date);
        }

        return result;
      },
    );
  }

  // * Delete plan ================================
  Future<bool> deletePlan(String planId) async {
    return await baseFunction(
      () async {
        final result = await planRepo.deletePlan(planId);

        if (result) {
          await getMyPlansForDate(_selectedDate);
          if (_selectedPlan?.id == planId) {
            _selectedPlan = null;
            notifyListeners();
          }
        }

        return result;
      },
    );
  }

  // * Append attendance to plan ================================
  Future<bool> appendAttendanceToPlan({
    required String mandobId,
    required String clientId,
    required String date,
    required AttendanceMap attendance,
    bool autoCreatePlan = true,
  }) async {
    return await baseFunction(
      () async {
        final result = await planRepo.appendAttendanceToPlan(
          mandobId: mandobId,
          clientId: clientId,
          date: date,
          attendance: attendance,
          autoCreatePlan: autoCreatePlan,
        );

        if (result) {
          await getMyPlansForDate(date);
        }

        return result;
      },
    );
  }

  // * Get my plans for today ================================
  Future<void> getMyPlansForToday() async {
    await getMyPlansForDate(DateTime.now().formatDateToString);
  }

  // * Get my plans for date ================================
  Future<void> getMyPlansForDate(String date) async {
    await baseFunction(
      () async {
        _selectedDate = date;
        _plans = await planRepo.getMyPlansForDate(date);
        notifyListeners();
      },
    );
  }

  // * Get plans by date and mandob (admin) ================================
  Future<void> getPlansByDateAndMandob({
    required String date,
    required String mandobId,
  }) async {
    await baseFunction(
      () async {
        _selectedDate = date;
        _plans = await planRepo.getPlansByDateAndMandob(
          date: date,
          mandobId: mandobId,
        );
        notifyListeners();
      },
    );
  }

  // * Get plans by date range ================================
  Future<void> getPlansByDateRange({
    required String startDate,
    required String endDate,
    String? mandobId,
  }) async {
    await baseFunction(
      () async {
        _plans = await planRepo.getPlansByDateRange(
          startDate: startDate,
          endDate: endDate,
          mandobId: mandobId,
        );
        notifyListeners();
      },
    );
  }

  // * Set selected plan ================================
  void setSelectedPlan(PlanModel? plan) {
    _selectedPlan = plan;
    notifyListeners();
  }

  // * Set selected date ================================
  void setSelectedDate(String date) {
    _selectedDate = date;
    getMyPlansForDate(date);
  }

  // * Clear selected plan ================================
  void clearSelectedPlan() {
    _selectedPlan = null;
    notifyListeners();
  }

  // * Get plan by ID ================================
  Future<PlanModel?> getPlanById(String planId) async {
    return await baseFunction(
      () async {
        return await planRepo.getPlanById(planId);
      },
    );
  }

  // * Stream methods ================================
  Stream<List<PlanModel>> getMyPlansStreamForDate(String date) {
    return planRepo.getMyPlansStreamForDate(date);
  }

  Stream<List<PlanModel>> getPlansStreamByDateAndMandob({
    required String date,
    required String mandobId,
  }) {
    return planRepo.getPlansStreamByDateAndMandob(
      date: date,
      mandobId: mandobId,
    );
  }

  Stream<List<PlanModel>> getPlansByDateRangeStream({
    required String startDate,
    required String endDate,
    String? mandobId,
  }) {
    return planRepo.getPlansByDateRangeStream(
      startDate: startDate,
      endDate: endDate,
      mandobId: mandobId,
    );
  }

  // * Helper methods ================================
  List<PlanModel> get plannedPlans => _plans.where((p) => p.isPlanned).toList();
  List<PlanModel> get attendedPlans => _plans.where((p) => p.isAttended).toList();
  List<PlanModel> get missedPlans => _plans.where((p) => p.isMissed).toList();
  List<PlanModel> get leadPlans => _plans.where((p) => p.isLead).toList();
  List<PlanModel> get dealPlans => _plans.where((p) => p.isDeal).toList();

  PlanModel? findPlanByClientAndDate(String clientId, String date) {
    try {
      return _plans.firstWhere(
        (plan) => plan.clientId == clientId && plan.date == date,
      );
    } catch (e) {
      return null;
    }
  }

  List<PlanModel> searchPlans(String query) {
    if (query.isEmpty) return _plans;
    
    final lowerQuery = query.toLowerCase();
    return _plans.where((plan) {
      return plan.clientName.toLowerCase().contains(lowerQuery) ||
             plan.note.toLowerCase().contains(lowerQuery) ||
             plan.type.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  // * Statistics ================================
  int get totalPlans => _plans.length;
  int get totalAttended => attendedPlans.length;
  int get totalMissed => missedPlans.length;
  int get totalPlanned => plannedPlans.length;

  double get attendanceRate {
    if (totalPlans == 0) return 0.0;
    return (totalAttended / totalPlans) * 100;
  }
}
