import 'package:equatable/equatable.dart';
import 'package:xr_helper/xr_helper.dart';

class AttendanceMap extends Equatable {
  final String id;
  final String date;
  final String uid;
  final String mandobName;
  final String mandobEmail;
  final double lat;
  final double long;
  final String place;
  final int timestamp; // milliseconds since epoch
  final bool isFirstOfDay;
  final String signatureUrl;

  const AttendanceMap({
    this.id = '',
    this.date = '',
    this.uid = '',
    this.mandobName = '',
    this.mandobEmail = '',
    this.lat = 0.0,
    this.long = 0.0,
    this.place = '',
    this.timestamp = 0,
    this.isFirstOfDay = false,
    this.signatureUrl = '',
  });

  factory AttendanceMap.fromJson(Map<String, dynamic> json) {
    return AttendanceMap(
      id: json['id'] ?? '',
      date: json['date'] ?? '',
      uid: json['uid'] ?? '',
      mandobName: json['mandob_name'] ?? '',
      mandobEmail: json['mandob_email'] ?? '',
      lat: (json['lat'] ?? 0.0).toDouble(),
      long: (json['long'] ?? 0.0).toDouble(),
      place: json['place'] ?? '',
      timestamp: json['timestamp'] ?? 0,
      isFirstOfDay: json['isFirstOfDay'] ?? false,
      signatureUrl: json['signature_url'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'date': date,
      'uid': uid,
      'mandob_name': mandobName,
      'mandob_email': mandobEmail,
      'lat': lat,
      'long': long,
      'place': place,
      'timestamp': timestamp,
      'isFirstOfDay': isFirstOfDay,
      'signature_url': signatureUrl,
    };
  }

  AttendanceMap copyWith({
    String? id,
    String? date,
    String? uid,
    String? mandobName,
    String? mandobEmail,
    double? lat,
    double? long,
    String? place,
    int? timestamp,
    bool? isFirstOfDay,
    String? signatureUrl,
  }) {
    return AttendanceMap(
      id: id ?? this.id,
      date: date ?? this.date,
      uid: uid ?? this.uid,
      mandobName: mandobName ?? this.mandobName,
      mandobEmail: mandobEmail ?? this.mandobEmail,
      lat: lat ?? this.lat,
      long: long ?? this.long,
      place: place ?? this.place,
      timestamp: timestamp ?? this.timestamp,
      isFirstOfDay: isFirstOfDay ?? this.isFirstOfDay,
      signatureUrl: signatureUrl ?? this.signatureUrl,
    );
  }

  @override
  List<Object?> get props => [
        id,
        date,
        uid,
        mandobName,
        mandobEmail,
        lat,
        long,
        place,
        timestamp,
        isFirstOfDay,
        signatureUrl,
      ];

  // Helper methods
  DateTime get dateTime => DateTime.fromMillisecondsSinceEpoch(timestamp);
  String get formattedTime => dateTime.formatTimeToString;
  String get formattedDate => dateTime.formatDateToString;
}

class PlanModel extends Equatable {
  final String id;
  final String date; // YYYY-MM-DD format
  final String mandobId;
  final String clientId;
  final String clientName;
  final String startTime; // HH:MM format
  final String endTime; // HH:MM format
  final String type; // 'Lead' or 'Deal'
  final String note;
  final String status; // 'planned', 'attended', 'missed'
  final List<AttendanceMap> attendances;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const PlanModel({
    this.id = '',
    this.date = '',
    this.mandobId = '',
    this.clientId = '',
    this.clientName = '',
    this.startTime = '',
    this.endTime = '',
    this.type = 'Lead',
    this.note = '',
    this.status = 'planned',
    this.attendances = const [],
    this.createdAt,
    this.updatedAt,
  });

  // * From Firestore Json ================================
  factory PlanModel.fromJson(Map<String, dynamic> json) {
    final attendancesList = json['attendances'] as List<dynamic>? ?? [];
    final attendances = attendancesList
        .map((attendance) => AttendanceMap.fromJson(attendance as Map<String, dynamic>))
        .toList();

    return PlanModel(
      id: json['id'] ?? '',
      date: json['date'] ?? '',
      mandobId: json['mandob_id'] ?? '',
      clientId: json['client_id'] ?? '',
      clientName: json['client_name'] ?? '',
      startTime: json['start_time'] ?? '',
      endTime: json['end_time'] ?? '',
      type: json['type'] ?? 'Lead',
      note: json['note'] ?? '',
      status: json['status'] ?? 'planned',
      attendances: attendances,
      createdAt: json['createdAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['createdAt'])
          : null,
      updatedAt: json['updatedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['updatedAt'])
          : null,
    );
  }

  // * To Firestore Json ================================
  Map<String, dynamic> toJson() {
    final now = DateTime.now();
    return {
      'id': id,
      'date': date,
      'mandob_id': mandobId,
      'client_id': clientId,
      'client_name': clientName,
      'start_time': startTime,
      'end_time': endTime,
      'type': type,
      'note': note,
      'status': status,
      'attendances': attendances.map((a) => a.toJson()).toList(),
      'createdAt': createdAt?.millisecondsSinceEpoch ?? now.millisecondsSinceEpoch,
      'updatedAt': now.millisecondsSinceEpoch,
    };
  }

  // * Copy With ================================
  PlanModel copyWith({
    String? id,
    String? date,
    String? mandobId,
    String? clientId,
    String? clientName,
    String? startTime,
    String? endTime,
    String? type,
    String? note,
    String? status,
    List<AttendanceMap>? attendances,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PlanModel(
      id: id ?? this.id,
      date: date ?? this.date,
      mandobId: mandobId ?? this.mandobId,
      clientId: clientId ?? this.clientId,
      clientName: clientName ?? this.clientName,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      type: type ?? this.type,
      note: note ?? this.note,
      status: status ?? this.status,
      attendances: attendances ?? this.attendances,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        date,
        mandobId,
        clientId,
        clientName,
        startTime,
        endTime,
        type,
        note,
        status,
        attendances,
        createdAt,
        updatedAt,
      ];

  // Helper methods
  bool get isPlanned => status == 'planned';
  bool get isAttended => status == 'attended';
  bool get isMissed => status == 'missed';
  bool get isLead => type == 'Lead';
  bool get isDeal => type == 'Deal';
  bool get hasAttendances => attendances.isNotEmpty;

  String get timeRange => '$startTime - $endTime';
  DateTime? get planDate => DateTime.tryParse(date);
  
  AttendanceMap? get latestAttendance {
    if (attendances.isEmpty) return null;
    return attendances.reduce((a, b) => a.timestamp > b.timestamp ? a : b);
  }

  String get displayStatus {
    switch (status) {
      case 'planned':
        return 'Planned';
      case 'attended':
        return 'Attended';
      case 'missed':
        return 'Missed';
      default:
        return status;
    }
  }
}
