import 'package:equatable/equatable.dart';
import 'package:xr_helper/xr_helper.dart';

class ClientModel extends Equatable {
  final String id;
  final String name;
  final String address;
  final double lat;
  final double long;
  final String type; // 'Lead' or 'Deal'
  final String mandobId;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const ClientModel({
    this.id = '',
    this.name = '',
    this.address = '',
    this.lat = 0.0,
    this.long = 0.0,
    this.type = 'Lead',
    this.mandobId = '',
    this.createdAt,
    this.updatedAt,
  });

  // * From Firestore Json ================================
  factory ClientModel.fromJson(Map<String, dynamic> json) {
    return ClientModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      address: json['address'] ?? '',
      lat: (json['lat'] ?? 0.0).toDouble(),
      long: (json['long'] ?? 0.0).toDouble(),
      type: json['type'] ?? 'Lead',
      mandobId: json['mandob_id'] ?? '',
      createdAt: json['createdAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['createdAt'])
          : null,
      updatedAt: json['updatedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['updatedAt'])
          : null,
    );
  }

  // * To Firestore Json ================================
  Map<String, dynamic> toJson() {
    final now = DateTime.now();
    return {
      'id': id,
      'name': name,
      'address': address,
      'lat': lat,
      'long': long,
      'type': type,
      'mandob_id': mandobId,
      'createdAt': createdAt?.millisecondsSinceEpoch ?? now.millisecondsSinceEpoch,
      'updatedAt': now.millisecondsSinceEpoch,
    };
  }

  // * Copy With ================================
  ClientModel copyWith({
    String? id,
    String? name,
    String? address,
    double? lat,
    double? long,
    String? type,
    String? mandobId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ClientModel(
      id: id ?? this.id,
      name: name ?? this.name,
      address: address ?? this.address,
      lat: lat ?? this.lat,
      long: long ?? this.long,
      type: type ?? this.type,
      mandobId: mandobId ?? this.mandobId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        address,
        lat,
        long,
        type,
        mandobId,
        createdAt,
        updatedAt,
      ];

  // Helper methods
  bool get isLead => type == 'Lead';
  bool get isDeal => type == 'Deal';

  String get displayName => name.isNotEmpty ? name : 'Unnamed Client';
  String get displayAddress => address.isNotEmpty ? address : 'No address';
}
