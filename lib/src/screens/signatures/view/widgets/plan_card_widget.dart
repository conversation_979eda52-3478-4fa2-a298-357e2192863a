import 'package:flutter/material.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/signatures/models/plan_model.dart';
import 'package:opti_tickets/src/screens/signatures/view/plan_details_modal.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:xr_helper/xr_helper.dart';

class PlanCardWidget extends StatelessWidget {
  final PlanModel plan;
  final VoidCallback? onTap;

  const PlanCardWidget({
    super.key,
    required this.plan,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
        border: _getBorderForStatus(),
      ),
      child: InkWell(
        onTap: onTap ?? () => _showPlanDetails(context),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header row with time and status
              Row(
                children: [
                  // Time range
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: ColorManager.primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      plan.timeRange,
                      style: AppTextStyles.labelMedium.copyWith(
                        color: ColorManager.primaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const Spacer(),
                  // Status badge
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getStatusColor().withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      _getStatusText(context),
                      style: AppTextStyles.labelSmall.copyWith(
                        color: _getStatusColor(),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // Client name and type
              Row(
                children: [
                  Expanded(
                    child: Text(
                      plan.clientName,
                      style: AppTextStyles.title,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: plan.isLead 
                          ? Colors.orange.shade100 
                          : Colors.green.shade100,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      plan.isLead ? context.tr.lead : context.tr.deal,
                      style: TextStyle(
                        color: plan.isLead 
                            ? Colors.orange.shade700 
                            : Colors.green.shade700,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),

              // Note (if available)
              if (plan.note.isNotEmpty) ...[
                const SizedBox(height: 8),
                Text(
                  plan.note,
                  style: AppTextStyles.subTitle.copyWith(
                    color: Colors.grey[600],
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],

              // Attendance info (if attended)
              if (plan.hasAttendances) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.green.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.green.shade200),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: Colors.green.shade600,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          '${context.tr.attended} ${plan.latestAttendance?.formattedTime ?? ''}',
                          style: TextStyle(
                            color: Colors.green.shade700,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      if (plan.latestAttendance?.signatureUrl.isNotEmpty == true)
                        Icon(
                          Icons.image,
                          color: Colors.green.shade600,
                          size: 16,
                        ),
                    ],
                  ),
                ),
              ],

              // Action buttons
              const SizedBox(height: 12),
              Row(
                children: [
                  // View details button
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _showPlanDetails(context),
                      icon: const Icon(Icons.info_outline, size: 16),
                      label: Text(context.tr.planDetails),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  // Map button (if has location)
                  if (plan.hasAttendances && plan.latestAttendance != null)
                    IconButton(
                      onPressed: () => _openMap(plan.latestAttendance!),
                      icon: const Icon(Icons.map, size: 20),
                      style: IconButton.styleFrom(
                        backgroundColor: ColorManager.primaryColor.withOpacity(0.1),
                        foregroundColor: ColorManager.primaryColor,
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Border? _getBorderForStatus() {
    switch (plan.status) {
      case 'attended':
        return Border.all(color: Colors.green, width: 2);
      case 'missed':
        return Border.all(color: Colors.red, width: 2);
      default:
        return null;
    }
  }

  Color _getStatusColor() {
    switch (plan.status) {
      case 'attended':
        return Colors.green;
      case 'missed':
        return Colors.red;
      case 'planned':
      default:
        return Colors.blue;
    }
  }

  String _getStatusText(BuildContext context) {
    switch (plan.status) {
      case 'attended':
        return context.tr.attended;
      case 'missed':
        return context.tr.missed;
      case 'planned':
      default:
        return context.tr.planned;
    }
  }

  void _showPlanDetails(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => PlanDetailsModal(plan: plan),
    );
  }

  Future<void> _openMap(AttendanceMap attendance) async {
    final url = 'https://www.google.com/maps/search/?api=1&query=${attendance.lat},${attendance.long}';
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    }
  }
}
