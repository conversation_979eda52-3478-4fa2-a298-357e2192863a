import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:geolocator/geolocator.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti_tickets/src/core/services/location_service.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/shared/widgets/loading/loading_widget.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/signatures/models/client_model.dart';
import 'package:opti_tickets/src/screens/signatures/providers/signature_providers.dart';
import 'package:xr_helper/xr_helper.dart';

class AddClientSheet extends HookConsumerWidget {
  final ClientModel? client; // null for add, non-null for edit

  const AddClientSheet({super.key, this.client});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final nameController = useTextEditingController(text: client?.name ?? '');
    final addressController = useTextEditingController(text: client?.address ?? '');
    final isLoading = useState(false);
    final selectedType = useState(client?.type ?? 'Lead');
    final currentLocation = useState<Position?>(null);

    // Initialize location if editing existing client
    useEffect(() {
      if (client != null && client!.lat != 0.0 && client!.long != 0.0) {
        currentLocation.value = Position(
          latitude: client!.lat,
          longitude: client!.long,
          timestamp: DateTime.now(),
          accuracy: 0,
          altitude: 0,
          altitudeAccuracy: 0,
          heading: 0,
          headingAccuracy: 0,
          speed: 0,
          speedAccuracy: 0,
        );
      }
      return null;
    }, []);

    Future<void> getCurrentLocation() async {
      try {
        isLoading.value = true;
        final position = await LocationService.getCurrentPosition();
        currentLocation.value = position;
        
        // Get address from coordinates
        final address = await LocationService.getAddressFromCoordinates(
          position.latitude,
          position.longitude,
        );
        if (address.isNotEmpty && addressController.text.isEmpty) {
          addressController.text = address;
        }
        
        Fluttertoast.showToast(msg: context.tr.locationObtained);
      } catch (e) {
        Fluttertoast.showToast(
          msg: e.toString(),
          backgroundColor: Colors.red,
        );
      } finally {
        isLoading.value = false;
      }
    }

    Future<void> saveClient() async {
      if (nameController.text.trim().isEmpty) {
        Fluttertoast.showToast(
          msg: context.tr.clientNameRequired,
          backgroundColor: Colors.red,
        );
        return;
      }

      if (addressController.text.trim().isEmpty) {
        Fluttertoast.showToast(
          msg: context.tr.clientAddressRequired,
          backgroundColor: Colors.red,
        );
        return;
      }

      if (currentLocation.value == null) {
        Fluttertoast.showToast(
          msg: context.tr.locationRequired,
          backgroundColor: Colors.red,
        );
        return;
      }

      isLoading.value = true;

      try {
        final controller = ref.read(clientControllerProvider);
        bool success;

        if (client == null) {
          // Add new client
          success = await controller.addClient(
            name: nameController.text.trim(),
            address: addressController.text.trim(),
            lat: currentLocation.value!.latitude,
            long: currentLocation.value!.longitude,
            type: selectedType.value,
          );
        } else {
          // Update existing client
          success = await controller.updateClient(
            clientId: client!.id,
            name: nameController.text.trim(),
            address: addressController.text.trim(),
            lat: currentLocation.value!.latitude,
            long: currentLocation.value!.longitude,
            type: selectedType.value,
          );
        }

        if (success) {
          Navigator.of(context).pop(true);
          Fluttertoast.showToast(
            msg: client == null 
                ? context.tr.clientAddedSuccessfully 
                : context.tr.clientUpdatedSuccessfully,
          );
        } else {
          Fluttertoast.showToast(
            msg: client == null 
                ? context.tr.clientAddFailed 
                : context.tr.clientUpdateFailed,
            backgroundColor: Colors.red,
          );
        }
      } catch (e) {
        Fluttertoast.showToast(msg: e.toString(), backgroundColor: Colors.red);
      } finally {
        isLoading.value = false;
      }
    }

    return Container(
      padding: EdgeInsets.only(
        left: 20,
        right: 20,
        top: 20,
        bottom: MediaQuery.of(context).viewInsets.bottom + 20,
      ),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Header
          Row(
            children: [
              Text(
                client == null ? context.tr.addClient : context.tr.editClient,
                style: AppTextStyles.title,
              ),
              const Spacer(),
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.close),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Client name input
          TextFormField(
            controller: nameController,
            decoration: InputDecoration(
              labelText: context.tr.clientName,
              hintText: context.tr.enterClientName,
              prefixIcon: const Icon(Icons.person),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            textInputAction: TextInputAction.next,
          ),
          const SizedBox(height: 16),

          // Client type selection
          DropdownButtonFormField<String>(
            value: selectedType.value,
            decoration: InputDecoration(
              labelText: context.tr.clientType,
              prefixIcon: const Icon(Icons.category),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            items: [
              DropdownMenuItem(
                value: 'Lead',
                child: Text(context.tr.lead),
              ),
              DropdownMenuItem(
                value: 'Deal',
                child: Text(context.tr.deal),
              ),
            ],
            onChanged: (value) {
              if (value != null) {
                selectedType.value = value;
              }
            },
          ),
          const SizedBox(height: 16),

          // Address input
          TextFormField(
            controller: addressController,
            decoration: InputDecoration(
              labelText: context.tr.address,
              hintText: context.tr.enterAddress,
              prefixIcon: const Icon(Icons.location_on),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            maxLines: 2,
            textInputAction: TextInputAction.done,
          ),
          const SizedBox(height: 16),

          // Location section
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: currentLocation.value != null 
                  ? Colors.green.shade50 
                  : Colors.orange.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: currentLocation.value != null 
                    ? Colors.green.shade200 
                    : Colors.orange.shade200,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      currentLocation.value != null 
                          ? Icons.location_on 
                          : Icons.location_off,
                      color: currentLocation.value != null 
                          ? Colors.green.shade600 
                          : Colors.orange.shade600,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        currentLocation.value != null 
                            ? context.tr.locationObtained 
                            : context.tr.locationRequired,
                        style: TextStyle(
                          color: currentLocation.value != null 
                              ? Colors.green.shade700 
                              : Colors.orange.shade700,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    TextButton(
                      onPressed: isLoading.value ? null : getCurrentLocation,
                      child: Text(context.tr.getLocation),
                    ),
                  ],
                ),
                if (currentLocation.value != null) ...[
                  const SizedBox(height: 8),
                  Text(
                    '${context.tr.coordinates}: ${currentLocation.value!.latitude.toStringAsFixed(6)}, ${currentLocation.value!.longitude.toStringAsFixed(6)}',
                    style: TextStyle(
                      color: Colors.green.shade600,
                      fontSize: 12,
                    ),
                  ),
                ],
              ],
            ),
          ),
          const SizedBox(height: 20),

          // Loading indicator
          if (isLoading.value) ...[
            const LoadingWidget(
              isLinear: true,
              color: ColorManager.primaryColor,
            ),
            const SizedBox(height: 16),
          ],

          // Save button
          Button(
            onPressed: isLoading.value ? null : saveClient,
            label: client == null ? context.tr.addClient : context.tr.updateClient,
          ),

          const SizedBox(height: 10),

          // Cancel button
          TextButton(
            onPressed: isLoading.value ? null : () => Navigator.of(context).pop(),
            child: Text(context.tr.cancel),
          ),
        ],
      ),
    );
  }
}
