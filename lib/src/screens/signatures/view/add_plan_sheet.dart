import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/shared/widgets/fields/base_search_sheet.dart';
import 'package:opti_tickets/src/core/shared/widgets/loading/loading_widget.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/auth/models/user_model.dart';
import 'package:opti_tickets/src/screens/signatures/models/client_model.dart';
import 'package:opti_tickets/src/screens/signatures/models/plan_model.dart';
import 'package:opti_tickets/src/screens/signatures/providers/signature_providers.dart';
import 'package:xr_helper/xr_helper.dart';

class AddPlanSheet extends HookConsumerWidget {
  final PlanModel? plan; // null for add, non-null for edit
  final String? selectedDate; // Pre-selected date

  const AddPlanSheet({super.key, this.plan, this.selectedDate});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final noteController = useTextEditingController(text: plan?.note ?? '');
    final isLoading = useState(false);
    final selectedClient = useState<ClientModel?>(null);
    final selectedType = useState(plan?.type ?? 'Lead');
    final planDate = useState(selectedDate ?? plan?.date ?? DateTime.now().formatDateToString);
    final startTime = useState(plan?.startTime ?? '09:00');
    final endTime = useState(plan?.endTime ?? '10:00');
    final currentUser = UserModel.currentUser();

    // Watch clients stream
    final clientsStream = currentUser.isAdmin 
        ? ref.watch(allClientsStreamProvider)
        : ref.watch(myClientsStreamProvider);

    // Initialize selected client if editing
    useEffect(() {
      if (plan != null) {
        clientsStream.whenData((clients) {
          final client = clients.where((c) => c.id == plan!.clientId).firstOrNull;
          if (client != null) {
            selectedClient.value = client;
          }
        });
      }
      return null;
    }, [plan]);

    Future<void> selectDate() async {
      final date = await showDatePicker(
        context: context,
        initialDate: DateTime.tryParse(planDate.value) ?? DateTime.now(),
        firstDate: DateTime.now().subtract(const Duration(days: 30)),
        lastDate: DateTime.now().add(const Duration(days: 365)),
      );
      
      if (date != null) {
        planDate.value = date.formatDateToString;
      }
    }

    Future<void> selectTime(bool isStartTime) async {
      final currentTime = isStartTime ? startTime.value : endTime.value;
      final timeParts = currentTime.split(':');
      final initialTime = TimeOfDay(
        hour: int.tryParse(timeParts[0]) ?? 9,
        minute: int.tryParse(timeParts[1]) ?? 0,
      );

      final time = await showTimePicker(
        context: context,
        initialTime: initialTime,
      );

      if (time != null) {
        final timeString = '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
        if (isStartTime) {
          startTime.value = timeString;
        } else {
          endTime.value = timeString;
        }
      }
    }

    Future<void> savePlan() async {
      if (selectedClient.value == null) {
        Fluttertoast.showToast(
          msg: context.tr.selectClient,
          backgroundColor: Colors.red,
        );
        return;
      }

      // Validate time range
      final startTimeParts = startTime.value.split(':');
      final endTimeParts = endTime.value.split(':');
      final startMinutes = int.parse(startTimeParts[0]) * 60 + int.parse(startTimeParts[1]);
      final endMinutes = int.parse(endTimeParts[0]) * 60 + int.parse(endTimeParts[1]);

      if (endMinutes <= startMinutes) {
        Fluttertoast.showToast(
          msg: context.tr.endTimeMustBeAfterStartTime,
          backgroundColor: Colors.red,
        );
        return;
      }

      isLoading.value = true;

      try {
        final controller = ref.read(planControllerProvider);
        bool success;

        if (plan == null) {
          // Add new plan
          success = await controller.addPlan(
            date: planDate.value,
            clientId: selectedClient.value!.id,
            clientName: selectedClient.value!.name,
            startTime: startTime.value,
            endTime: endTime.value,
            type: selectedType.value,
            note: noteController.text.trim(),
          );
        } else {
          // Update existing plan
          success = await controller.updatePlan(
            planId: plan!.id,
            date: planDate.value,
            clientId: selectedClient.value!.id,
            clientName: selectedClient.value!.name,
            startTime: startTime.value,
            endTime: endTime.value,
            type: selectedType.value,
            note: noteController.text.trim(),
          );
        }

        if (success) {
          Navigator.of(context).pop(true);
          Fluttertoast.showToast(
            msg: plan == null 
                ? context.tr.planAddedSuccessfully 
                : context.tr.planUpdatedSuccessfully,
          );
        } else {
          Fluttertoast.showToast(
            msg: plan == null 
                ? context.tr.planAddFailed 
                : context.tr.planUpdateFailed,
            backgroundColor: Colors.red,
          );
        }
      } catch (e) {
        Fluttertoast.showToast(msg: e.toString(), backgroundColor: Colors.red);
      } finally {
        isLoading.value = false;
      }
    }

    return Container(
      padding: EdgeInsets.only(
        left: 20,
        right: 20,
        top: 20,
        bottom: MediaQuery.of(context).viewInsets.bottom + 20,
      ),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Header
          Row(
            children: [
              Text(
                plan == null ? context.tr.addPlan : context.tr.editPlan,
                style: AppTextStyles.title,
              ),
              const Spacer(),
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.close),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Client selection
          clientsStream.when(
            data: (clients) => BaseSearchSheet(
              selectedValue: selectedClient.value,
              label: context.tr.selectClient,
              data: clients,
              onChanged: (client) => selectedClient.value = client,
              itemModelAsName: (client) => (client as ClientModel).name,
            ),
            loading: () => const SizedBox(
              height: 60,
              child: Center(child: CircularProgressIndicator()),
            ),
            error: (error, stack) => Container(
              height: 60,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.red),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Center(
                child: Text(
                  context.tr.errorLoadingClients,
                  style: const TextStyle(color: Colors.red),
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Date selection
          InkWell(
            onTap: selectDate,
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  const Icon(Icons.calendar_today),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      planDate.value,
                      style: AppTextStyles.subTitle,
                    ),
                  ),
                  const Icon(Icons.arrow_drop_down),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Time selection
          Row(
            children: [
              Expanded(
                child: InkWell(
                  onTap: () => selectTime(true),
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.access_time),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                context.tr.startTime,
                                style: AppTextStyles.greyHint,
                              ),
                              Text(
                                startTime.value,
                                style: AppTextStyles.subTitle,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: InkWell(
                  onTap: () => selectTime(false),
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.access_time),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                context.tr.endTime,
                                style: AppTextStyles.greyHint,
                              ),
                              Text(
                                endTime.value,
                                style: AppTextStyles.subTitle,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Type selection
          DropdownButtonFormField<String>(
            value: selectedType.value,
            decoration: InputDecoration(
              labelText: context.tr.clientType,
              prefixIcon: const Icon(Icons.category),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            items: [
              DropdownMenuItem(
                value: 'Lead',
                child: Text(context.tr.lead),
              ),
              DropdownMenuItem(
                value: 'Deal',
                child: Text(context.tr.deal),
              ),
            ],
            onChanged: (value) {
              if (value != null) {
                selectedType.value = value;
              }
            },
          ),
          const SizedBox(height: 16),

          // Note input
          TextFormField(
            controller: noteController,
            decoration: InputDecoration(
              labelText: context.tr.note,
              hintText: context.tr.enterNote,
              prefixIcon: const Icon(Icons.note),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            maxLines: 3,
            textInputAction: TextInputAction.done,
          ),
          const SizedBox(height: 20),

          // Loading indicator
          if (isLoading.value) ...[
            const LoadingWidget(
              isLinear: true,
              color: ColorManager.primaryColor,
            ),
            const SizedBox(height: 16),
          ],

          // Save button
          Button(
            onPressed: isLoading.value ? null : savePlan,
            label: plan == null ? context.tr.addPlan : context.tr.editPlan,
          ),

          const SizedBox(height: 10),

          // Cancel button
          TextButton(
            onPressed: isLoading.value ? null : () => Navigator.of(context).pop(),
            child: Text(context.tr.cancel),
          ),
        ],
      ),
    );
  }
}
