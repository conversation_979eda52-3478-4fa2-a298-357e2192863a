import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/auth/models/user_model.dart';
import 'package:opti_tickets/src/screens/signatures/models/client_model.dart';
import 'package:opti_tickets/src/screens/signatures/providers/signature_providers.dart';
import 'package:opti_tickets/src/screens/signatures/view/add_client_sheet.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:xr_helper/xr_helper.dart';

class ClientsScreen extends HookConsumerWidget {
  const ClientsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final searchController = useTextEditingController();
    final searchQuery = useState('');
    final currentUser = UserModel.currentUser();

    // Watch clients stream
    final clientsStream = currentUser.isAdmin 
        ? ref.watch(allClientsStreamProvider)
        : ref.watch(myClientsStreamProvider);

    void showAddClientSheet([ClientModel? client]) {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) => AddClientSheet(client: client),
      ).then((result) {
        if (result == true) {
          // Refresh handled by stream
        }
      });
    }

    Future<void> deleteClient(ClientModel client) async {
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(context.tr.deleteClient),
          content: Text(context.tr.deleteClientConfirmation.replaceAll('{name}', client.name)),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(context.tr.cancel),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: Text(context.tr.delete),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        try {
          final controller = ref.read(clientControllerProvider);
          final success = await controller.deleteClient(client.id);
          
          if (success) {
            Fluttertoast.showToast(msg: context.tr.clientDeletedSuccessfully);
          } else {
            Fluttertoast.showToast(
              msg: context.tr.clientDeleteFailed,
              backgroundColor: Colors.red,
            );
          }
        } catch (e) {
          Fluttertoast.showToast(msg: e.toString(), backgroundColor: Colors.red);
        }
      }
    }

    Future<void> openMap(ClientModel client) async {
      final url = 'https://www.google.com/maps/search/?api=1&query=${client.lat},${client.long}';
      if (await canLaunchUrl(Uri.parse(url))) {
        await launchUrl(Uri.parse(url));
      } else {
        Fluttertoast.showToast(
          msg: context.tr.cannotOpenMap,
          backgroundColor: Colors.red,
        );
      }
    }

    List<ClientModel> filterClients(List<ClientModel> clients) {
      if (searchQuery.value.isEmpty) return clients;
      
      final query = searchQuery.value.toLowerCase();
      return clients.where((client) {
        return client.name.toLowerCase().contains(query) ||
               client.address.toLowerCase().contains(query) ||
               client.type.toLowerCase().contains(query);
      }).toList();
    }

    return Scaffold(
      backgroundColor: ColorManager.lightGreyBackground,
      appBar: AppBar(
        title: Text(context.tr.clients, style: AppTextStyles.whiteTitle),
        backgroundColor: ColorManager.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: () => showAddClientSheet(),
            icon: const Icon(Icons.add),
          ),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Container(
            margin: const EdgeInsets.all(16),
            child: TextField(
              controller: searchController,
              decoration: InputDecoration(
                hintText: context.tr.searchClients,
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: Colors.white,
              ),
              onChanged: (value) => searchQuery.value = value,
            ),
          ),

          // Clients list
          Expanded(
            child: clientsStream.when(
              data: (clients) {
                final filteredClients = filterClients(clients);
                
                if (filteredClients.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.people_outline,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          searchQuery.value.isEmpty 
                              ? context.tr.noClientsFound 
                              : context.tr.noClientsMatchSearch,
                          style: AppTextStyles.subTitle.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                        if (searchQuery.value.isEmpty) ...[
                          const SizedBox(height: 16),
                          ElevatedButton.icon(
                            onPressed: () => showAddClientSheet(),
                            icon: const Icon(Icons.add),
                            label: Text(context.tr.addFirstClient),
                          ),
                        ],
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: filteredClients.length,
                  itemBuilder: (context, index) {
                    final client = filteredClients[index];
                    
                    return Container(
                      margin: const EdgeInsets.only(bottom: 12),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withOpacity(0.1),
                            spreadRadius: 1,
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: ListTile(
                        contentPadding: const EdgeInsets.all(16),
                        leading: Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            color: client.isLead 
                                ? Colors.orange.shade100 
                                : Colors.green.shade100,
                            borderRadius: BorderRadius.circular(25),
                          ),
                          child: Icon(
                            client.isLead ? Icons.person_search : Icons.handshake,
                            color: client.isLead 
                                ? Colors.orange.shade600 
                                : Colors.green.shade600,
                          ),
                        ),
                        title: Text(
                          client.name,
                          style: AppTextStyles.title,
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: 4),
                            Text(
                              client.address,
                              style: AppTextStyles.subTitle.copyWith(
                                color: Colors.grey[600],
                              ),
                            ),
                            const SizedBox(height: 4),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: client.isLead 
                                    ? Colors.orange.shade100 
                                    : Colors.green.shade100,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                client.isLead ? context.tr.lead : context.tr.deal,
                                style: TextStyle(
                                  color: client.isLead 
                                      ? Colors.orange.shade700 
                                      : Colors.green.shade700,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),
                        trailing: PopupMenuButton<String>(
                          onSelected: (value) {
                            switch (value) {
                              case 'edit':
                                showAddClientSheet(client);
                                break;
                              case 'map':
                                openMap(client);
                                break;
                              case 'delete':
                                deleteClient(client);
                                break;
                            }
                          },
                          itemBuilder: (context) => [
                            PopupMenuItem(
                              value: 'edit',
                              child: Row(
                                children: [
                                  const Icon(Icons.edit, size: 20),
                                  const SizedBox(width: 8),
                                  Text(context.tr.edit),
                                ],
                              ),
                            ),
                            PopupMenuItem(
                              value: 'map',
                              child: Row(
                                children: [
                                  const Icon(Icons.map, size: 20),
                                  const SizedBox(width: 8),
                                  Text(context.tr.showOnMap),
                                ],
                              ),
                            ),
                            PopupMenuItem(
                              value: 'delete',
                              child: Row(
                                children: [
                                  const Icon(Icons.delete, size: 20, color: Colors.red),
                                  const SizedBox(width: 8),
                                  Text(context.tr.delete, style: const TextStyle(color: Colors.red)),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Colors.red[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      context.tr.errorLoadingClients,
                      style: AppTextStyles.subTitle.copyWith(
                        color: Colors.red[600],
                      ),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => ref.refresh(currentUser.isAdmin 
                          ? allClientsStreamProvider 
                          : myClientsStreamProvider),
                      child: Text(context.tr.retry),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
