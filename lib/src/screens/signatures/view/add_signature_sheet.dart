import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/shared/widgets/loading/loading_widget.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/signatures/providers/signature_providers.dart';
import 'package:xr_helper/xr_helper.dart';

class AddSignatureSheet extends HookConsumerWidget {
  const AddSignatureSheet({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final placeController = useTextEditingController();
    final isLoading = useState(false);

    Future<void> addSignature() async {
      if (placeController.text.trim().isEmpty) {
        Fluttertoast.showToast(
          msg: context.tr.enterPlace,
          backgroundColor: Colors.red,
        );
        return;
      }

      isLoading.value = true;

      try {
        final controller = ref.read(signatureControllerProvider);
        final success = await controller.addSignature(
          place: placeController.text.trim(),
          requireAuth: true,
        );

        if (success) {
          Navigator.of(context).pop(true);
          Fluttertoast.showToast(msg: context.tr.signatureAddedSuccessfully);
        } else {
          Fluttertoast.showToast(
              msg: context.tr.signatureAddFailed, backgroundColor: Colors.red);
        }
      } catch (e) {
        Fluttertoast.showToast(msg: e.toString(), backgroundColor: Colors.red);
      } finally {
        isLoading.value = false;
      }
    }

    final canAddFirst = ref.watch(canAddFirstSignatureProvider);
    final locationPermission = ref.watch(locationPermissionProvider);
    final biometricAvailable = ref.watch(biometricAvailabilityProvider);

    return Container(
      padding: EdgeInsets.only(
        left: 20,
        right: 20,
        top: 20,
        bottom: MediaQuery.of(context).viewInsets.bottom + 20,
      ),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Handle bar
          Center(
            child: Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          const SizedBox(height: 20),

          // Title
          Text(
            context.tr.addSignature,
            style: context.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),

          // Permission checks
          ...canAddFirst.when(
            loading: () => [
              const LoadingWidget(
                isLinear: true,
                color: ColorManager.primaryColor,
              ),
            ],
            error: (e, s) => [
              const SizedBox(),
            ],
            data: (canAdd) => canAdd
                ? [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.green.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.green.shade200),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.info, color: Colors.green.shade600),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              context.tr.officeSignatures,
                              style: TextStyle(color: Colors.green.shade700),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                  ]
                : [],
          ),

          // Location permission check
          ...locationPermission.when(
            data: (hasPermission) => hasPermission
                ? []
                : [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.red.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.red.shade200),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.location_off, color: Colors.red.shade600),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              context.tr.locationPermissionDenied,
                              style: TextStyle(color: Colors.red.shade700),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],
            loading: () => [
              const LoadingWidget(
                isLinear: true,
                color: ColorManager.primaryColor,
              ),
            ],
            error: (e, s) => [
              const SizedBox(),
            ],
          ),

          // Biometric availability check
          ...biometricAvailable.when(
            loading: () => [
              const LoadingWidget(
                isLinear: true,
                color: ColorManager.primaryColor,
              ),
            ],
            error: (e, s) => [
              const SizedBox(),
            ],
            data: (isAvailable) => isAvailable
                ? []
                : [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.orange.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.orange.shade200),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.fingerprint,
                              color: Colors.orange.shade600),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              context.tr.biometricNotAvailable,
                              style: TextStyle(color: Colors.orange.shade700),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],
          ),

          // Place input
          TextFormField(
            controller: placeController,
            decoration: InputDecoration(
              labelText: context.tr.place,
              hintText: context.tr.enterPlace,
              prefixIcon: const Icon(Icons.location_on),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            textInputAction: TextInputAction.done,
            onFieldSubmitted: (_) => addSignature(),
          ),
          const SizedBox(height: 20),

          // Add button
          Button(
            onPressed: isLoading.value ? null : addSignature,
            label: context.tr.addSignature,
          ),

          const SizedBox(height: 10),

          // Cancel button
          TextButton(
            onPressed:
                isLoading.value ? null : () => Navigator.of(context).pop(),
            child: Text(context.tr.cancel),
          ),
        ],
      ),
    );
  }
}
