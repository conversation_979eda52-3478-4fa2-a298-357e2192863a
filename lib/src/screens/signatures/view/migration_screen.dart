import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti_tickets/src/core/services/migration_service.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/shared/widgets/loading/loading_widget.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/auth/models/user_model.dart';
import 'package:xr_helper/xr_helper.dart';

class MigrationScreen extends HookConsumerWidget {
  const MigrationScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isLoading = useState(false);
    final migrationCompleted = useState<bool?>(null);
    final currentUser = UserModel.currentUser();

    // Check if user is admin
    if (!currentUser.isAdmin) {
      return Scaffold(
        appBar: AppBar(
          title: Text(context.tr.migration),
        ),
        body: const Center(
          child: Text('Access denied. Admin only.'),
        ),
      );
    }

    useEffect(() {
      // Check migration status on load
      MigrationService.isMigrationCompleted().then((completed) {
        migrationCompleted.value = completed;
      });
      return null;
    }, []);

    Future<void> runMigration({bool dryRun = false}) async {
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(dryRun ? 'Run Dry Migration' : 'Run Migration'),
          content: Text(
            dryRun
                ? 'This will simulate the migration process without making any changes. Continue?'
                : 'This will migrate all signatures to the new plans structure. A backup will be created. Continue?',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(context.tr.cancel),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text(context.tr.confirm),
            ),
          ],
        ),
      );

      if (confirmed != true) return;

      isLoading.value = true;

      try {
        final success = await MigrationService.migrateSignaturesToPlans(
          createBackup: true,
          dryRun: dryRun,
        );

        if (success) {
          Fluttertoast.showToast(
            msg: dryRun
                ? 'Dry run completed successfully'
                : 'Migration completed successfully',
          );
          
          if (!dryRun) {
            migrationCompleted.value = true;
          }
        } else {
          Fluttertoast.showToast(
            msg: 'Migration failed',
            backgroundColor: Colors.red,
          );
        }
      } catch (e) {
        Fluttertoast.showToast(
          msg: 'Migration error: $e',
          backgroundColor: Colors.red,
        );
      } finally {
        isLoading.value = false;
      }
    }

    Future<void> rollbackMigration() async {
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Rollback Migration'),
          content: const Text(
            'This will delete all plans and restore signatures from backup. This action cannot be undone. Continue?',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(context.tr.cancel),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              child: const Text('Rollback'),
            ),
          ],
        ),
      );

      if (confirmed != true) return;

      isLoading.value = true;

      try {
        final success = await MigrationService.rollbackMigration();

        if (success) {
          Fluttertoast.showToast(msg: 'Rollback completed successfully');
          migrationCompleted.value = false;
        } else {
          Fluttertoast.showToast(
            msg: 'Rollback failed',
            backgroundColor: Colors.red,
          );
        }
      } catch (e) {
        Fluttertoast.showToast(
          msg: 'Rollback error: $e',
          backgroundColor: Colors.red,
        );
      } finally {
        isLoading.value = false;
      }
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(context.tr.migration),
        backgroundColor: ColorManager.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Status card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          migrationCompleted.value == true
                              ? Icons.check_circle
                              : migrationCompleted.value == false
                                  ? Icons.pending
                                  : Icons.help,
                          color: migrationCompleted.value == true
                              ? Colors.green
                              : migrationCompleted.value == false
                                  ? Colors.orange
                                  : Colors.grey,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Migration Status',
                          style: AppTextStyles.title,
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      migrationCompleted.value == true
                          ? 'Migration has been completed'
                          : migrationCompleted.value == false
                              ? 'Migration not completed'
                              : 'Checking migration status...',
                      style: AppTextStyles.subTitle,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Migration info
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'About Migration',
                      style: AppTextStyles.title,
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '• Converts signatures to plans with attendances\n'
                      '• Groups signatures by mandob, date, and location\n'
                      '• Creates automatic backup before migration\n'
                      '• Excludes biometric data (security requirement)\n'
                      '• Can be rolled back if needed',
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Loading indicator
            if (isLoading.value) ...[
              const LoadingWidget(
                isLinear: true,
                color: ColorManager.primaryColor,
              ),
              const SizedBox(height: 16),
            ],

            // Action buttons
            if (migrationCompleted.value != true) ...[
              ElevatedButton.icon(
                onPressed: isLoading.value ? null : () => runMigration(dryRun: true),
                icon: const Icon(Icons.preview),
                label: const Text('Run Dry Migration'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
              const SizedBox(height: 12),
              ElevatedButton.icon(
                onPressed: isLoading.value ? null : () => runMigration(dryRun: false),
                icon: const Icon(Icons.play_arrow),
                label: const Text('Run Migration'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: ColorManager.primaryColor,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ] else ...[
              ElevatedButton.icon(
                onPressed: isLoading.value ? null : rollbackMigration,
                icon: const Icon(Icons.undo),
                label: const Text('Rollback Migration'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ],

            const Spacer(),

            // Warning
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                border: Border.all(color: Colors.orange.shade200),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.warning,
                    color: Colors.orange.shade700,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Admin only feature. Migration affects all users. Always run dry migration first.',
                      style: TextStyle(
                        color: Colors.orange.shade700,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
