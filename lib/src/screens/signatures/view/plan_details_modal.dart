import 'package:flutter/material.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/signatures/models/plan_model.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:xr_helper/xr_helper.dart';

class PlanDetailsModal extends StatelessWidget {
  final PlanModel plan;

  const PlanDetailsModal({super.key, required this.plan});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Text(
                context.tr.planDetails,
                style: AppTextStyles.title,
              ),
              const Spacer(),
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.close),
              ),
            ],
          ),
          const SizedBox(height: 20),

          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Plan Info Section
                  _buildSectionCard(
                    context,
                    title: context.tr.planInfo,
                    icon: Icons.event,
                    children: [
                      _buildInfoRow(context.tr.clientName, plan.clientName),
                      _buildInfoRow(context.tr.planDate, plan.date),
                      _buildInfoRow(context.tr.timeRange, plan.timeRange),
                      _buildInfoRow(context.tr.clientType, plan.isLead ? context.tr.lead : context.tr.deal),
                      if (plan.note.isNotEmpty)
                        _buildInfoRow(context.tr.note, plan.note),
                      _buildStatusRow(context),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Client Info Section (if available)
                  _buildSectionCard(
                    context,
                    title: context.tr.clientInfo,
                    icon: Icons.person,
                    children: [
                      _buildInfoRow(context.tr.clientName, plan.clientName),
                      _buildInfoRow(context.tr.clientType, plan.isLead ? context.tr.lead : context.tr.deal),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Attendances Section
                  _buildSectionCard(
                    context,
                    title: '${context.tr.attendances} (${plan.attendances.length})',
                    icon: Icons.check_circle,
                    children: plan.attendances.isEmpty
                        ? [
                            Container(
                              padding: const EdgeInsets.all(16),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.info_outline,
                                    color: Colors.grey[400],
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    context.tr.noAttendances,
                                    style: AppTextStyles.subTitle.copyWith(
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ]
                        : plan.attendances.map((attendance) => _buildAttendanceCard(context, attendance)).toList(),
                  ),

                  const SizedBox(height: 16),

                  // Statistics Section
                  _buildSectionCard(
                    context,
                    title: context.tr.statistics,
                    icon: Icons.analytics,
                    children: [
                      _buildInfoRow(context.tr.totalAttendances, plan.attendances.length.toString()),
                      _buildInfoRow(context.tr.planStatus, _getStatusText(context)),
                      if (plan.hasAttendances)
                        _buildInfoRow(context.tr.attendanceTime, plan.latestAttendance?.formattedTime ?? ''),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // Action buttons
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                  label: Text(context.tr.close),
                ),
              ),
              if (plan.hasAttendances && plan.latestAttendance != null) ...[
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _openMap(plan.latestAttendance!),
                    icon: const Icon(Icons.map),
                    label: Text(context.tr.showOnMap),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: ColorManager.primaryColor,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSectionCard(
    BuildContext context, {
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: ColorManager.primaryColor.withOpacity(0.1),
              borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
            ),
            child: Row(
              children: [
                Icon(icon, color: ColorManager.primaryColor, size: 20),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: AppTextStyles.boldSubTitle.copyWith(
                    color: ColorManager.primaryColor,
                  ),
                ),
              ],
            ),
          ),
          // Section content
          ...children,
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: AppTextStyles.labelMedium.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.subTitle,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusRow(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              context.tr.planStatus,
              style: AppTextStyles.labelMedium.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ),
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getStatusColor().withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              _getStatusText(context),
              style: AppTextStyles.labelMedium.copyWith(
                color: _getStatusColor(),
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAttendanceCard(BuildContext context, AttendanceMap attendance) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.access_time,
                color: Colors.green[600],
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                attendance.formattedTime,
                style: AppTextStyles.boldSubTitle.copyWith(
                  color: Colors.green[600],
                ),
              ),
              const Spacer(),
              if (attendance.isFirstOfDay)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.orange.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    context.tr.firstOfDay,
                    style: TextStyle(
                      color: Colors.orange.shade700,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            attendance.place,
            style: AppTextStyles.subTitle,
          ),
          const SizedBox(height: 4),
          Text(
            '${context.tr.coordinates}: ${attendance.lat.toStringAsFixed(6)}, ${attendance.long.toStringAsFixed(6)}',
            style: AppTextStyles.greyHint,
          ),
          if (attendance.signatureUrl.isNotEmpty) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  Icons.image,
                  color: Colors.blue[600],
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  context.tr.signatureImage,
                  style: AppTextStyles.labelMedium.copyWith(
                    color: Colors.blue[600],
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Color _getStatusColor() {
    switch (plan.status) {
      case 'attended':
        return Colors.green;
      case 'missed':
        return Colors.red;
      case 'planned':
      default:
        return Colors.blue;
    }
  }

  String _getStatusText(BuildContext context) {
    switch (plan.status) {
      case 'attended':
        return context.tr.attended;
      case 'missed':
        return context.tr.missed;
      case 'planned':
      default:
        return context.tr.planned;
    }
  }

  Future<void> _openMap(AttendanceMap attendance) async {
    final url = 'https://www.google.com/maps/search/?api=1&query=${attendance.lat},${attendance.long}';
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    }
  }
}
