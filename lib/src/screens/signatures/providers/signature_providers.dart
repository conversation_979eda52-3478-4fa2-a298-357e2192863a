import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti_tickets/src/screens/signatures/controllers/client_controller.dart';
import 'package:opti_tickets/src/screens/signatures/controllers/plan_controller.dart';
import 'package:opti_tickets/src/screens/signatures/controllers/signature_controller.dart';
import 'package:opti_tickets/src/screens/signatures/models/client_model.dart';
import 'package:opti_tickets/src/screens/signatures/models/plan_model.dart';
import 'package:opti_tickets/src/screens/signatures/models/signature_model.dart';
import 'package:opti_tickets/src/screens/signatures/repositories/client_repository.dart';
import 'package:opti_tickets/src/screens/signatures/repositories/plan_repository.dart';
import 'package:opti_tickets/src/screens/signatures/repositories/signature_repository.dart';
import 'package:xr_helper/xr_helper.dart';

// * Signature Repository Provider ========================================
final signatureRepoProvider = Provider<SignatureRepository>((ref) {
  return SignatureRepository();
});

// * Signature Controller Provider ========================================
final signatureControllerProvider = ChangeNotifierProvider<SignatureController>(
  (ref) {
    final signatureRepo = ref.watch(signatureRepoProvider);

    return SignatureController(
      signatureRepo: signatureRepo,
    );
  },
);

// * Today's Signatures Future Provider ========================================
final todaySignaturesFutureProvider = FutureProvider((ref) async {
  final signatureRepo = ref.watch(signatureRepoProvider);
  return await signatureRepo.getTodaySignatures();
});

// * Signatures by Date Future Provider ========================================
final signaturesByDateFutureProvider =
    FutureProvider.family<List<SignatureModel>, DateTime>((ref, date) async {
  final signatureRepo = ref.watch(signatureRepoProvider);
  final dateString = date.formatDateToString;
  return await signatureRepo.getSignaturesByDate(date: dateString);
});

// * Users Future Provider (for admin) ========================================
final usersFutureProvider = FutureProvider((ref) async {
  final signatureRepo = ref.watch(signatureRepoProvider);
  return await signatureRepo.getAllUsers();
});

// * Client Repository Provider ========================================
final clientRepoProvider = Provider<ClientRepository>((ref) {
  return ClientRepository();
});

// * Client Controller Provider ========================================
final clientControllerProvider = ChangeNotifierProvider<ClientController>(
  (ref) {
    final clientRepo = ref.watch(clientRepoProvider);

    return ClientController(
      clientRepo: clientRepo,
    );
  },
);

// * My Clients Future Provider ========================================
final myClientsFutureProvider = FutureProvider((ref) async {
  final clientRepo = ref.watch(clientRepoProvider);
  return await clientRepo.getMyClients();
});

// * All Clients Future Provider (admin) ========================================
final allClientsFutureProvider = FutureProvider((ref) async {
  final clientRepo = ref.watch(clientRepoProvider);
  return await clientRepo.getAllClients();
});

// * My Clients Stream Provider ========================================
final myClientsStreamProvider = StreamProvider<List<ClientModel>>((ref) {
  final clientRepo = ref.watch(clientRepoProvider);
  return clientRepo.getMyClientsStream();
});

// * All Clients Stream Provider (admin) ========================================
final allClientsStreamProvider = StreamProvider<List<ClientModel>>((ref) {
  final clientRepo = ref.watch(clientRepoProvider);
  return clientRepo.getAllClientsStream();
});

// * Plan Repository Provider ========================================
final planRepoProvider = Provider<PlanRepository>((ref) {
  return PlanRepository();
});

// * Plan Controller Provider ========================================
final planControllerProvider = ChangeNotifierProvider<PlanController>(
  (ref) {
    final planRepo = ref.watch(planRepoProvider);

    return PlanController(
      planRepo: planRepo,
    );
  },
);

// * My Plans for Date Future Provider ========================================
final myPlansForDateFutureProvider =
    FutureProvider.family<List<PlanModel>, String>((ref, date) async {
  final planRepo = ref.watch(planRepoProvider);
  return await planRepo.getMyPlansForDate(date);
});

// * Plans by Date and Mandob Future Provider (admin) ========================================
final plansByDateAndMandobFutureProvider =
    FutureProvider.family<List<PlanModel>, (String, String)>(
        (ref, params) async {
  final planRepo = ref.watch(planRepoProvider);
  return await planRepo.getPlansByDateAndMandob(
    date: params.$1,
    mandobId: params.$2,
  );
});

// * My Plans for Date Stream Provider ========================================
final myPlansForDateStreamProvider =
    StreamProvider.family<List<PlanModel>, String>((ref, date) {
  final planRepo = ref.watch(planRepoProvider);
  return planRepo.getMyPlansStreamForDate(date);
});

// * Plans by Date and Mandob Stream Provider (admin) ========================================
final plansByDateAndMandobStreamProvider =
    StreamProvider.family<List<PlanModel>, (String, String)>((ref, params) {
  final planRepo = ref.watch(planRepoProvider);
  return planRepo.getPlansStreamByDateAndMandob(
    date: params.$1,
    mandobId: params.$2,
  );
});

// * Can Add First Signature Provider ========================================
final canAddFirstSignatureProvider = FutureProvider<bool>((ref) async {
  final signatureRepo = ref.watch(signatureRepoProvider);
  return await signatureRepo.canAddFirstSignature();
});

// * Location Permission Provider ========================================
final locationPermissionProvider = FutureProvider<bool>((ref) async {
  final signatureRepo = ref.watch(signatureRepoProvider);
  return await signatureRepo.checkLocationPermission();
});

// * Biometric Availability Provider ========================================
final biometricAvailabilityProvider = FutureProvider<bool>((ref) async {
  final signatureRepo = ref.watch(signatureRepoProvider);
  return await signatureRepo.checkBiometricAvailability();
});

// * Stream Providers ========================================

// * Get Signatures Stream Controller Provider ========================================
final getSignaturesStreamControllerProvider = StreamProvider.family
    .autoDispose<List<SignatureModel>, (DateTime, String?)>(
  (ref, params) {
    final signatureController = ref.watch(
      signatureControllerProvider,
    );

    return signatureController.getSignaturesStream(
      date: params.$1,
      userId: params.$2,
    );
  },
);

// * Get Signatures by Date Range Stream Provider (for admin reports) ========================================
final getSignaturesByDateRangeStreamProvider = StreamProvider.family
    .autoDispose<List<SignatureModel>, (DateTime, DateTime, String?)>(
  (ref, params) {
    final signatureController = ref.watch(signatureControllerProvider);

    return signatureController.getSignaturesByDateRangeStream(
      startDate: params.$1,
      endDate: params.$2,
      uid: params.$3,
    );
  },
);
