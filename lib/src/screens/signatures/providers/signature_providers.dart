import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti_tickets/src/screens/signatures/controllers/signature_controller.dart';
import 'package:opti_tickets/src/screens/signatures/models/signature_model.dart';
import 'package:opti_tickets/src/screens/signatures/repositories/signature_repository.dart';
import 'package:xr_helper/xr_helper.dart';

// * Signature Repository Provider ========================================
final signatureRepoProvider = Provider<SignatureRepository>((ref) {
  return SignatureRepository();
});

// * Signature Controller Provider ========================================
final signatureControllerProvider = ChangeNotifierProvider<SignatureController>(
  (ref) {
    final signatureRepo = ref.watch(signatureRepoProvider);

    return SignatureController(
      signatureRepo: signatureRepo,
    );
  },
);

// * Today's Signatures Future Provider ========================================
final todaySignaturesFutureProvider = FutureProvider((ref) async {
  final signatureRepo = ref.watch(signatureRepoProvider);
  return await signatureRepo.getTodaySignatures();
});

// * Signatures by Date Future Provider ========================================
final signaturesByDateFutureProvider =
    FutureProvider.family<List<SignatureModel>, DateTime>((ref, date) async {
  final signatureRepo = ref.watch(signatureRepoProvider);
  final dateString = date.formatDateToString;
  return await signatureRepo.getSignaturesByDate(date: dateString);
});

// * Users Future Provider (for admin) ========================================
final usersFutureProvider = FutureProvider((ref) async {
  final signatureRepo = ref.watch(signatureRepoProvider);
  return await signatureRepo.getAllUsers();
});

// * Can Add First Signature Provider ========================================
final canAddFirstSignatureProvider = FutureProvider<bool>((ref) async {
  final signatureRepo = ref.watch(signatureRepoProvider);
  return await signatureRepo.canAddFirstSignature();
});

// * Location Permission Provider ========================================
final locationPermissionProvider = FutureProvider<bool>((ref) async {
  final signatureRepo = ref.watch(signatureRepoProvider);
  return await signatureRepo.checkLocationPermission();
});

// * Biometric Availability Provider ========================================
final biometricAvailabilityProvider = FutureProvider<bool>((ref) async {
  final signatureRepo = ref.watch(signatureRepoProvider);
  return await signatureRepo.checkBiometricAvailability();
});

// * Stream Providers ========================================

// * Get Signatures Stream Controller Provider ========================================
final getSignaturesStreamControllerProvider = StreamProvider.family
    .autoDispose<List<SignatureModel>, (DateTime, String?)>(
  (ref, params) {
    final signatureController = ref.watch(
      signatureControllerProvider,
    );

    return signatureController.getSignaturesStream(
      date: params.$1,
      userId: params.$2,
    );
  },
);

// * Get Signatures by Date Range Stream Provider (for admin reports) ========================================
final getSignaturesByDateRangeStreamProvider = StreamProvider.family
    .autoDispose<List<SignatureModel>, (DateTime, DateTime, String?)>(
  (ref, params) {
    final signatureController = ref.watch(signatureControllerProvider);

    return signatureController.getSignaturesByDateRangeStream(
      startDate: params.$1,
      endDate: params.$2,
      uid: params.$3,
    );
  },
);
