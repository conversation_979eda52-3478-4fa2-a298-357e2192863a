import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:opti_tickets/src/screens/auth/models/user_model.dart';
import 'package:opti_tickets/src/screens/signatures/models/client_model.dart';
import 'package:xr_helper/xr_helper.dart';

class ClientRepository extends BaseRepository {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _clientsCollection = 'clients';

  // * Add client ================================
  Future<bool> addClient({
    required String name,
    required String address,
    required double lat,
    required double long,
    required String type,
  }) async {
    return baseFunction(
      () async {
        final currentUser = UserModel.currentUser();
        if (currentUser.uid.isEmpty) {
          throw Exception('User not logged in');
        }

        final docRef = _firestore.collection(_clientsCollection).doc();
        final client = ClientModel(
          id: docRef.id,
          name: name,
          address: address,
          lat: lat,
          long: long,
          type: type,
          mandobId: currentUser.uid,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await docRef.set(client.toJson());
        Log.i('Client added successfully: $name');
        return true;
      },
    );
  }

  // * Update client ================================
  Future<bool> updateClient({
    required String clientId,
    required String name,
    required String address,
    required double lat,
    required double long,
    required String type,
  }) async {
    return baseFunction(
      () async {
        final currentUser = UserModel.currentUser();
        if (currentUser.uid.isEmpty) {
          throw Exception('User not logged in');
        }

        final docRef = _firestore.collection(_clientsCollection).doc(clientId);
        final docSnapshot = await docRef.get();
        
        if (!docSnapshot.exists) {
          throw Exception('Client not found');
        }

        final existingClient = ClientModel.fromJson(docSnapshot.data()!);
        
        // Check if user owns this client or is admin
        if (!currentUser.isAdmin && existingClient.mandobId != currentUser.uid) {
          throw Exception('Unauthorized to update this client');
        }

        final updatedClient = existingClient.copyWith(
          name: name,
          address: address,
          lat: lat,
          long: long,
          type: type,
          updatedAt: DateTime.now(),
        );

        await docRef.update(updatedClient.toJson());
        Log.i('Client updated successfully: $name');
        return true;
      },
    );
  }

  // * Delete client ================================
  Future<bool> deleteClient(String clientId) async {
    return baseFunction(
      () async {
        final currentUser = UserModel.currentUser();
        if (currentUser.uid.isEmpty) {
          throw Exception('User not logged in');
        }

        final docRef = _firestore.collection(_clientsCollection).doc(clientId);
        final docSnapshot = await docRef.get();
        
        if (!docSnapshot.exists) {
          throw Exception('Client not found');
        }

        final client = ClientModel.fromJson(docSnapshot.data()!);
        
        // Check if user owns this client or is admin
        if (!currentUser.isAdmin && client.mandobId != currentUser.uid) {
          throw Exception('Unauthorized to delete this client');
        }

        await docRef.delete();
        Log.i('Client deleted successfully: ${client.name}');
        return true;
      },
    );
  }

  // * Get clients for current user ================================
  Future<List<ClientModel>> getMyClients() async {
    return baseFunction(
      () async {
        final currentUser = UserModel.currentUser();
        if (currentUser.uid.isEmpty) {
          throw Exception('User not logged in');
        }

        Query query = _firestore
            .collection(_clientsCollection)
            .where('mandob_id', isEqualTo: currentUser.uid)
            .orderBy('name');

        final querySnapshot = await query.get();
        return querySnapshot.docs
            .map((doc) => ClientModel.fromJson(doc.data() as Map<String, dynamic>))
            .toList();
      },
    );
  }

  // * Get all clients (admin only) ================================
  Future<List<ClientModel>> getAllClients() async {
    return baseFunction(
      () async {
        final currentUser = UserModel.currentUser();
        if (currentUser.uid.isEmpty) {
          throw Exception('User not logged in');
        }

        if (!currentUser.isAdmin) {
          throw Exception('Admin access required');
        }

        final querySnapshot = await _firestore
            .collection(_clientsCollection)
            .orderBy('name')
            .get();

        return querySnapshot.docs
            .map((doc) => ClientModel.fromJson(doc.data() as Map<String, dynamic>))
            .toList();
      },
    );
  }

  // * Get clients by mandob (admin only) ================================
  Future<List<ClientModel>> getClientsByMandob(String mandobId) async {
    return baseFunction(
      () async {
        final currentUser = UserModel.currentUser();
        if (currentUser.uid.isEmpty) {
          throw Exception('User not logged in');
        }

        if (!currentUser.isAdmin) {
          throw Exception('Admin access required');
        }

        final querySnapshot = await _firestore
            .collection(_clientsCollection)
            .where('mandob_id', isEqualTo: mandobId)
            .orderBy('name')
            .get();

        return querySnapshot.docs
            .map((doc) => ClientModel.fromJson(doc.data() as Map<String, dynamic>))
            .toList();
      },
    );
  }

  // * Get client by ID ================================
  Future<ClientModel?> getClientById(String clientId) async {
    return baseFunction(
      () async {
        final docSnapshot = await _firestore
            .collection(_clientsCollection)
            .doc(clientId)
            .get();

        if (!docSnapshot.exists) {
          return null;
        }

        return ClientModel.fromJson(docSnapshot.data()!);
      },
    );
  }

  // * Stream methods ================================
  Stream<List<ClientModel>> getMyClientsStream() {
    final currentUser = UserModel.currentUser();
    if (currentUser.uid.isEmpty) {
      return Stream.value([]);
    }

    return _firestore
        .collection(_clientsCollection)
        .where('mandob_id', isEqualTo: currentUser.uid)
        .orderBy('name')
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => ClientModel.fromJson(doc.data()))
            .toList());
  }

  Stream<List<ClientModel>> getAllClientsStream() {
    final currentUser = UserModel.currentUser();
    if (currentUser.uid.isEmpty || !currentUser.isAdmin) {
      return Stream.value([]);
    }

    return _firestore
        .collection(_clientsCollection)
        .orderBy('name')
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => ClientModel.fromJson(doc.data()))
            .toList());
  }
}
