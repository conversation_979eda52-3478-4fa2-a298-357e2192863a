import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:opti_tickets/src/screens/auth/models/user_model.dart';
import 'package:opti_tickets/src/screens/signatures/models/plan_model.dart';
import 'package:xr_helper/xr_helper.dart';

class PlanRepository extends BaseRepository {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _plansCollection = 'plans';

  // * Add plan ================================
  Future<bool> addPlan({
    required String date,
    required String clientId,
    required String clientName,
    required String startTime,
    required String endTime,
    required String type,
    String note = '',
  }) async {
    return baseFunction(
      () async {
        final currentUser = UserModel.currentUser();
        if (currentUser.uid.isEmpty) {
          throw Exception('User not logged in');
        }

        final docRef = _firestore.collection(_plansCollection).doc();
        final plan = PlanModel(
          id: docRef.id,
          date: date,
          mandobId: currentUser.uid,
          clientId: clientId,
          clientName: clientName,
          startTime: startTime,
          endTime: endTime,
          type: type,
          note: note,
          status: 'planned',
          attendances: [],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await docRef.set(plan.toJson());
        Log.i('Plan added successfully: $clientName on $date');
        return true;
      },
    );
  }

  // * Update plan ================================
  Future<bool> updatePlan({
    required String planId,
    required String date,
    required String clientId,
    required String clientName,
    required String startTime,
    required String endTime,
    required String type,
    String note = '',
  }) async {
    return baseFunction(
      () async {
        final currentUser = UserModel.currentUser();
        if (currentUser.uid.isEmpty) {
          throw Exception('User not logged in');
        }

        final docRef = _firestore.collection(_plansCollection).doc(planId);
        final docSnapshot = await docRef.get();

        if (!docSnapshot.exists) {
          throw Exception('Plan not found');
        }

        final existingPlan = PlanModel.fromJson(docSnapshot.data()!);

        // Check if user owns this plan or is admin
        if (!currentUser.isAdmin && existingPlan.mandobId != currentUser.uid) {
          throw Exception('Unauthorized to update this plan');
        }

        final updatedPlan = existingPlan.copyWith(
          date: date,
          clientId: clientId,
          clientName: clientName,
          startTime: startTime,
          endTime: endTime,
          type: type,
          note: note,
          updatedAt: DateTime.now(),
        );

        await docRef.update(updatedPlan.toJson());
        Log.i('Plan updated successfully: $clientName on $date');
        return true;
      },
    );
  }

  // * Delete plan ================================
  Future<bool> deletePlan(String planId) async {
    return baseFunction(
      () async {
        final currentUser = UserModel.currentUser();
        if (currentUser.uid.isEmpty) {
          throw Exception('User not logged in');
        }

        final docRef = _firestore.collection(_plansCollection).doc(planId);
        final docSnapshot = await docRef.get();

        if (!docSnapshot.exists) {
          throw Exception('Plan not found');
        }

        final plan = PlanModel.fromJson(docSnapshot.data()!);

        // Check if user owns this plan or is admin
        if (!currentUser.isAdmin && plan.mandobId != currentUser.uid) {
          throw Exception('Unauthorized to delete this plan');
        }

        await docRef.delete();
        Log.i('Plan deleted successfully: ${plan.clientName}');
        return true;
      },
    );
  }

  // * Append attendance to plan ================================
  Future<bool> appendAttendanceToPlan({
    required String mandobId,
    required String clientId,
    required String date,
    required AttendanceMap attendance,
    bool autoCreatePlan = true,
  }) async {
    return baseFunction(
      () async {
        // Use batch write for atomic operation
        final batch = _firestore.batch();

        // Find existing plan for this mandob, client, and date
        final planQuery = await _firestore
            .collection(_plansCollection)
            .where('mandob_id', isEqualTo: mandobId)
            .where('client_id', isEqualTo: clientId)
            .where('date', isEqualTo: date)
            .limit(1)
            .get();

        DocumentReference planRef;
        PlanModel plan;

        if (planQuery.docs.isNotEmpty) {
          // Update existing plan
          planRef = planQuery.docs.first.reference;
          plan = PlanModel.fromJson(planQuery.docs.first.data());

          final updatedAttendances = List<AttendanceMap>.from(plan.attendances)
            ..add(attendance);

          final updatedPlan = plan.copyWith(
            attendances: updatedAttendances,
            status: 'attended',
            updatedAt: DateTime.now(),
          );

          batch.update(planRef, updatedPlan.toJson());
        } else if (autoCreatePlan) {
          // Create new plan with attendance
          planRef = _firestore.collection(_plansCollection).doc();
          plan = PlanModel(
            id: planRef.id,
            date: date,
            mandobId: mandobId,
            clientId: clientId,
            clientName:
                attendance.place, // Use place as client name if not available
            startTime: DateTime.fromMillisecondsSinceEpoch(attendance.timestamp)
                .formatTimeToString,
            endTime: DateTime.fromMillisecondsSinceEpoch(attendance.timestamp)
                .add(const Duration(hours: 1))
                .formatTimeToString,
            type: 'Lead', // Default type
            note: 'Auto-created from attendance',
            status: 'attended',
            attendances: [attendance],
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          batch.set(planRef, plan.toJson());
        } else {
          throw Exception('No plan found for this client and date');
        }

        await batch.commit();
        Log.i('Attendance appended to plan successfully');
        return true;
      },
    );
  }

  // * Get plans by date and mandob ================================
  Future<List<PlanModel>> getPlansByDateAndMandob({
    required String date,
    required String mandobId,
  }) async {
    return baseFunction(
      () async {
        final querySnapshot = await _firestore
            .collection(_plansCollection)
            .where('date', isEqualTo: date)
            .where('mandob_id', isEqualTo: mandobId)
            .orderBy('start_time')
            .get();

        return querySnapshot.docs
            .map((doc) => PlanModel.fromJson(doc.data()))
            .toList();
      },
    );
  }

  // * Get my plans for date ================================
  Future<List<PlanModel>> getMyPlansForDate(String date) async {
    return baseFunction(
      () async {
        final currentUser = UserModel.currentUser();
        if (currentUser.uid.isEmpty) {
          throw Exception('User not logged in');
        }

        return await getPlansByDateAndMandob(
          date: date,
          mandobId: currentUser.uid,
        );
      },
    );
  }

  // * Get plans by date range ================================
  Future<List<PlanModel>> getPlansByDateRange({
    required String startDate,
    required String endDate,
    String? mandobId,
  }) async {
    return baseFunction(
      () async {
        Query query = _firestore
            .collection(_plansCollection)
            .where('date', isGreaterThanOrEqualTo: startDate)
            .where('date', isLessThanOrEqualTo: endDate);

        if (mandobId != null && mandobId.isNotEmpty) {
          query = query.where('mandob_id', isEqualTo: mandobId);
        }

        final querySnapshot = await query
            .orderBy('date', descending: true)
            .orderBy('start_time')
            .get();

        return querySnapshot.docs
            .map(
                (doc) => PlanModel.fromJson(doc.data() as Map<String, dynamic>))
            .toList();
      },
    );
  }

  // * Get plan by ID ================================
  Future<PlanModel?> getPlanById(String planId) async {
    return baseFunction(
      () async {
        final docSnapshot =
            await _firestore.collection(_plansCollection).doc(planId).get();

        if (!docSnapshot.exists) {
          return null;
        }

        return PlanModel.fromJson(docSnapshot.data()!);
      },
    );
  }

  // * Stream methods ================================
  Stream<List<PlanModel>> getPlansStreamByDateAndMandob({
    required String date,
    required String mandobId,
  }) {
    return _firestore
        .collection(_plansCollection)
        .where('date', isEqualTo: date)
        .where('mandob_id', isEqualTo: mandobId)
        .orderBy('start_time')
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => PlanModel.fromJson(doc.data()))
            .toList());
  }

  Stream<List<PlanModel>> getMyPlansStreamForDate(String date) {
    final currentUser = UserModel.currentUser();
    if (currentUser.uid.isEmpty) {
      return Stream.value([]);
    }

    return getPlansStreamByDateAndMandob(
      date: date,
      mandobId: currentUser.uid,
    );
  }

  Stream<List<PlanModel>> getPlansByDateRangeStream({
    required String startDate,
    required String endDate,
    String? mandobId,
  }) {
    Query query = _firestore
        .collection(_plansCollection)
        .where('date', isGreaterThanOrEqualTo: startDate)
        .where('date', isLessThanOrEqualTo: endDate);

    if (mandobId != null && mandobId.isNotEmpty) {
      query = query.where('mandob_id', isEqualTo: mandobId);
    }

    return query
        .orderBy('date', descending: true)
        .orderBy('start_time')
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map(
                (doc) => PlanModel.fromJson(doc.data() as Map<String, dynamic>))
            .toList());
  }
}
