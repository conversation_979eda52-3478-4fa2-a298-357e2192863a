// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
// import 'package:opti_tickets/src/core/theme/color_manager.dart';
// import 'package:opti_tickets/src/core/utils/show_modal_sheet.dart';
// import 'package:opti_tickets/src/screens/home/<USER>/home_model.dart';
// import 'package:opti_tickets/src/screens/home/<USER>/home_providers.dart';
// import 'package:opti_tickets/src/screens/main_screen/view/widgets/home_app_bar.dart';
// import 'package:skeletonizer/skeletonizer.dart';
// import 'package:xr_helper/xr_helper.dart';
//
// import '../../../core/shared/widgets/navigations/bottom_nav_bar.widget.dart';
//
// class MainScreen extends ConsumerWidget {
//   const MainScreen({super.key});
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final homeFuture = ref.watch(getHomeFutureProvider);
//
//     final homeModel = homeFuture.when(
//       data: (homeData) => homeData,
//       loading: () => const HomeModel(),
//       error: (error, stackTrace) => const HomeModel(),
//     );
//
//     final isLoading = homeFuture.isLoading;
//
//     final homeData = isLoading ? demoHomeModel : homeModel;
//
//     return Scaffold(
//       backgroundColor: ColorManager.lightGreyBackground,
//       bottomNavigationBar: const BottomNavBarWidget(),
//       body: CustomScrollView(
//         slivers: [
//         SliverAppBar(
//         automaticallyImplyLeading: false,
//         pinned: true,
//         expandedHeight: 110.h,
//         shape: const RoundedRectangleBorder(
//           borderRadius: BorderRadius.only(
//             bottomLeft: Radius.circular(AppRadius.radius24),
//             bottomRight: Radius.circular(AppRadius.radius24),
//           ),
//         ),
//         flexibleSpace: FlexibleSpaceBar(
//           background: Skeletonizer(
//             enabled: isLoading,
//             child: appBar: AppBar(
//             automaticallyImplyLeading: false,
//             title: Text(context.tr.signatures),
//             backgroundColor: ColorManager.primaryColor,
//             foregroundColor: Colors.white,
//             actions: [
//               if (currentUser.isAdmin)
//                 IconButton(
//                   icon: const Icon(Icons.assessment),
//                   onPressed: () {
//                     const ReportsScreen().navigate;
//                   },
//                 ),
//               IconButton(
//                 icon: const Icon(
//                   Icons.logout,
//                   color: Colors.white,
//                 ),
//                 onPressed: () {
//                   showDialog(
//                     context: context,
//                     builder: (context) =>
//                         AlertDialog(
//                           title: Text(context.tr.logoutConfirmation),
//                           content: Text(context.tr.areYouSureLogout),
//                           actions: [
//                             TextButton(
//                               onPressed: () => Navigator.of(context).pop(),
//                               child: Text(context.tr.cancel),
//                             ),
//                             TextButton(
//                               onPressed: () {
//                                 ref.read(authControllerProvider).logout();
//                               },
//                               child: Text(context.tr.logout),
//                             ),
//                           ],
//                         ),
//                   );
//                 },
//               ),
//             ],
//           ),
//
//         ),
//       ),
//     ),
//     const SliverToBoxAdapter(
//     child: AppGaps.gap16,
//     ),
//     SliverToBoxAdapter(
//     child: Skeletonizer(
//     enabled: isLoading,
//     child: ProgramsSliderWidget(
//     programs: homeData.programs,
//     ),
//     ),
//     ),
//     const SliverToBoxAdapter(
//     child: AppGaps.gap16,
//     ),
//     SliverToBoxAdapter(
//     child: Skeletonizer(
//     enabled: isLoading,
//     child: Padding(
//     padding:
//     const EdgeInsets.symmetric(horizontal: AppSpaces.padding16),
//     child: Text(
//     context.tr.recentActiveTickets,
//     style: AppTextStyles.title,
//     ),
//     ),
//     ),
//     ),
//     const SliverToBoxAdapter(
//     child: AppGaps.gap16,
//     ),
//     SliverPadding(
//     padding: const EdgeInsets.symmetric(
//     horizontal: AppSpaces.padding16,
//     ),
//     sliver: SliverList(
//     delegate: SliverChildBuilderDelegate(
//     (context, index) {
//     final ticket = homeData.tickets[index];
//     return Skeletonizer(
//     enabled: isLoading,
//     child: Padding(
//     padding: const EdgeInsets.only(
//     bottom: AppSpaces.padding16,
//     ),
//     child: TicketCard(
//     ticket: ticket,
//     ),
//     ),
//     );
//     },
//     childCount: homeData.tickets.length,
//     ),
//     ),
//     ),
//     const SliverToBoxAdapter(
//     child: AppGaps.gap16,
//     ),
//     ],
//     ),
//     );
//   }
// }
