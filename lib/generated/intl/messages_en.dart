// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  static String m0(name) => "Are you sure you want to delete ${name}?";

  static String m1(name) => "Welcome, ${name}";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "absents": MessageLookupByLibrary.simpleMessage("Absents"),
    "active": MessageLookupByLibrary.simpleMessage("Active"),
    "activeTasks": MessageLookupByLibrary.simpleMessage("Active Tasks"),
    "addClient": MessageLookupByLibrary.simpleMessage("Add Client"),
    "addFirstClient": MessageLookupByLibrary.simpleMessage("Add First Client"),
    "addFirstPlan": MessageLookupByLibrary.simpleMessage("Add First Plan"),
    "addNewTicket": MessageLookupByLibrary.simpleMessage("Add New Ticket"),
    "addPlan": MessageLookupByLibrary.simpleMessage("Add Plan"),
    "addSignature": MessageLookupByLibrary.simpleMessage("Add Signature"),
    "address": MessageLookupByLibrary.simpleMessage("Address"),
    "admin": MessageLookupByLibrary.simpleMessage("Admin"),
    "allTickets": MessageLookupByLibrary.simpleMessage("All Tickets"),
    "alreadyHaveAccount": MessageLookupByLibrary.simpleMessage(
      "Already have an account?",
    ),
    "arabic": MessageLookupByLibrary.simpleMessage("العربية"),
    "archived": MessageLookupByLibrary.simpleMessage("Archived"),
    "areYouSureLogout": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to logout?",
    ),
    "areYouSureYouWantToLogout": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to logout?",
    ),
    "ascending": MessageLookupByLibrary.simpleMessage("Ascending"),
    "attachToPlan": MessageLookupByLibrary.simpleMessage("Attach to Plan"),
    "attachToPlanDescription": MessageLookupByLibrary.simpleMessage(
      "Automatically create or update a plan for this client",
    ),
    "attachment": MessageLookupByLibrary.simpleMessage("Attachment"),
    "attendanceDetails": MessageLookupByLibrary.simpleMessage(
      "Attendance Details",
    ),
    "attendanceRate": MessageLookupByLibrary.simpleMessage("Attendance Rate"),
    "attendanceTime": MessageLookupByLibrary.simpleMessage("Attendance Time"),
    "attendances": MessageLookupByLibrary.simpleMessage("Attendances"),
    "attended": MessageLookupByLibrary.simpleMessage("Attended"),
    "authenticate": MessageLookupByLibrary.simpleMessage("Authenticate"),
    "authenticationRequired": MessageLookupByLibrary.simpleMessage(
      "Authentication Required",
    ),
    "biometricNotAvailable": MessageLookupByLibrary.simpleMessage(
      "Biometric authentication not available",
    ),
    "camera": MessageLookupByLibrary.simpleMessage("Camera"),
    "cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
    "cannotOpenMap": MessageLookupByLibrary.simpleMessage("Cannot open map"),
    "changeLanguage": MessageLookupByLibrary.simpleMessage("Change Language"),
    "checkIn": MessageLookupByLibrary.simpleMessage("Check In"),
    "client": MessageLookupByLibrary.simpleMessage("Client"),
    "clientAddFailed": MessageLookupByLibrary.simpleMessage(
      "Failed to add client",
    ),
    "clientAddedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Client added successfully",
    ),
    "clientAddress": MessageLookupByLibrary.simpleMessage("Client Address"),
    "clientAddressRequired": MessageLookupByLibrary.simpleMessage(
      "Client address is required",
    ),
    "clientDeleteFailed": MessageLookupByLibrary.simpleMessage(
      "Failed to delete client",
    ),
    "clientDeletedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Client deleted successfully",
    ),
    "clientInfo": MessageLookupByLibrary.simpleMessage("Client Information"),
    "clientName": MessageLookupByLibrary.simpleMessage("Client Name"),
    "clientNameRequired": MessageLookupByLibrary.simpleMessage(
      "Client name is required",
    ),
    "clientType": MessageLookupByLibrary.simpleMessage("Client Type"),
    "clientUpdateFailed": MessageLookupByLibrary.simpleMessage(
      "Failed to update client",
    ),
    "clientUpdatedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Client updated successfully",
    ),
    "clients": MessageLookupByLibrary.simpleMessage("Clients"),
    "close": MessageLookupByLibrary.simpleMessage("Close"),
    "completeAttends": MessageLookupByLibrary.simpleMessage("Complete Attends"),
    "confirm": MessageLookupByLibrary.simpleMessage("Confirm"),
    "coordinates": MessageLookupByLibrary.simpleMessage("Coordinates"),
    "createAccount": MessageLookupByLibrary.simpleMessage("Create Account"),
    "currentTime": MessageLookupByLibrary.simpleMessage("Current Time"),
    "dailyTimeline": MessageLookupByLibrary.simpleMessage("Daily Timeline"),
    "dark": MessageLookupByLibrary.simpleMessage("Dark"),
    "dateRange": MessageLookupByLibrary.simpleMessage("Date Range"),
    "days": MessageLookupByLibrary.simpleMessage("Days"),
    "deal": MessageLookupByLibrary.simpleMessage("Deal"),
    "delete": MessageLookupByLibrary.simpleMessage("Delete"),
    "deleteClient": MessageLookupByLibrary.simpleMessage("Delete Client"),
    "deleteClientConfirmation": m0,
    "deletePlan": MessageLookupByLibrary.simpleMessage("Delete Plan"),
    "deletePlanConfirmation": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete this plan?",
    ),
    "descending": MessageLookupByLibrary.simpleMessage("Descending"),
    "description": MessageLookupByLibrary.simpleMessage("Description"),
    "dontHaveAccount": MessageLookupByLibrary.simpleMessage(
      "Don\'t have an account?",
    ),
    "edit": MessageLookupByLibrary.simpleMessage("Edit"),
    "editClient": MessageLookupByLibrary.simpleMessage("Edit Client"),
    "editPlan": MessageLookupByLibrary.simpleMessage("Edit Plan"),
    "email": MessageLookupByLibrary.simpleMessage("Email"),
    "emailAlreadyInUse": MessageLookupByLibrary.simpleMessage(
      "Email already in use",
    ),
    "endDate": MessageLookupByLibrary.simpleMessage("End Date"),
    "endTime": MessageLookupByLibrary.simpleMessage("End Time"),
    "endTimeMustBeAfterStartTime": MessageLookupByLibrary.simpleMessage(
      "End time must be after start time",
    ),
    "english": MessageLookupByLibrary.simpleMessage("English"),
    "enter": MessageLookupByLibrary.simpleMessage("Enter"),
    "enterAddress": MessageLookupByLibrary.simpleMessage("Enter address"),
    "enterClientName": MessageLookupByLibrary.simpleMessage(
      "Enter client name",
    ),
    "enterNote": MessageLookupByLibrary.simpleMessage("Enter note"),
    "enterPlace": MessageLookupByLibrary.simpleMessage("Enter Place"),
    "errorLoadingClients": MessageLookupByLibrary.simpleMessage(
      "Error loading clients",
    ),
    "export": MessageLookupByLibrary.simpleMessage("Export"),
    "exportExcel": MessageLookupByLibrary.simpleMessage("Export Excel"),
    "exportPdf": MessageLookupByLibrary.simpleMessage("Export PDF"),
    "fingerprint": MessageLookupByLibrary.simpleMessage("Fingerprint"),
    "finishedTasks": MessageLookupByLibrary.simpleMessage("Finished Tasks"),
    "firstOfDay": MessageLookupByLibrary.simpleMessage("First of Day"),
    "fromDate": MessageLookupByLibrary.simpleMessage("From Date"),
    "gallery": MessageLookupByLibrary.simpleMessage("Gallery"),
    "getLocation": MessageLookupByLibrary.simpleMessage("Get Location"),
    "home": MessageLookupByLibrary.simpleMessage("Home"),
    "incompleteAttends": MessageLookupByLibrary.simpleMessage(
      "Incomplete Attends",
    ),
    "invalidCredentials": MessageLookupByLibrary.simpleMessage(
      "Invalid credentials",
    ),
    "issue": MessageLookupByLibrary.simpleMessage("Issue"),
    "issuerEmail": MessageLookupByLibrary.simpleMessage("Issuer Email"),
    "issuerName": MessageLookupByLibrary.simpleMessage("Issuer Name"),
    "issuerPhone": MessageLookupByLibrary.simpleMessage("Issuer Phone"),
    "itsGreatToSeeYou": MessageLookupByLibrary.simpleMessage(
      "It\'s great to see you",
    ),
    "language": MessageLookupByLibrary.simpleMessage("Language"),
    "lead": MessageLookupByLibrary.simpleMessage("Lead"),
    "light": MessageLookupByLibrary.simpleMessage("Light"),
    "location": MessageLookupByLibrary.simpleMessage("Location"),
    "locationObtained": MessageLookupByLibrary.simpleMessage(
      "Location obtained successfully",
    ),
    "locationPermissionDenied": MessageLookupByLibrary.simpleMessage(
      "Location permission denied",
    ),
    "locationRequired": MessageLookupByLibrary.simpleMessage(
      "Location Required",
    ),
    "login": MessageLookupByLibrary.simpleMessage("Login"),
    "loginFailed": MessageLookupByLibrary.simpleMessage("Login failed"),
    "logout": MessageLookupByLibrary.simpleMessage("Logout"),
    "logoutConfirmation": MessageLookupByLibrary.simpleMessage(
      "Logout Confirmation",
    ),
    "mandob": MessageLookupByLibrary.simpleMessage("Delivery"),
    "migration": MessageLookupByLibrary.simpleMessage("Migration"),
    "missed": MessageLookupByLibrary.simpleMessage("Missed"),
    "monthlyStats": MessageLookupByLibrary.simpleMessage("Monthly Statistics"),
    "mySubscriptions": MessageLookupByLibrary.simpleMessage("My Subscriptions"),
    "name": MessageLookupByLibrary.simpleMessage("Name"),
    "networkError": MessageLookupByLibrary.simpleMessage("Network error"),
    "newReplyOnTicket": MessageLookupByLibrary.simpleMessage(
      "New reply on ticket",
    ),
    "noAttendances": MessageLookupByLibrary.simpleMessage("No attendances"),
    "noClientsFound": MessageLookupByLibrary.simpleMessage("No clients found"),
    "noClientsMatchSearch": MessageLookupByLibrary.simpleMessage(
      "No clients match your search",
    ),
    "noDataAvailable": MessageLookupByLibrary.simpleMessage(
      "No data available",
    ),
    "noDataFound": MessageLookupByLibrary.simpleMessage("No data found"),
    "noPlansFound": MessageLookupByLibrary.simpleMessage("No plans found"),
    "noPlansTodayFound": MessageLookupByLibrary.simpleMessage(
      "No plans found for today",
    ),
    "noRepliesFound": MessageLookupByLibrary.simpleMessage("No replies found"),
    "noSignaturesToday": MessageLookupByLibrary.simpleMessage(
      "No signatures today",
    ),
    "note": MessageLookupByLibrary.simpleMessage("Note"),
    "office": MessageLookupByLibrary.simpleMessage("Office"),
    "officeSignature": MessageLookupByLibrary.simpleMessage("Office Signature"),
    "officeSignatures": MessageLookupByLibrary.simpleMessage(
      "Office Signatures",
    ),
    "officialHolidays": MessageLookupByLibrary.simpleMessage(
      "Official Holidays",
    ),
    "password": MessageLookupByLibrary.simpleMessage("Password"),
    "pickImage": MessageLookupByLibrary.simpleMessage("Pick Image"),
    "place": MessageLookupByLibrary.simpleMessage("Place"),
    "plan": MessageLookupByLibrary.simpleMessage("Plan"),
    "planAddFailed": MessageLookupByLibrary.simpleMessage("Failed to add plan"),
    "planAddedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Plan added successfully",
    ),
    "planDate": MessageLookupByLibrary.simpleMessage("Plan Date"),
    "planDeleteFailed": MessageLookupByLibrary.simpleMessage(
      "Failed to delete plan",
    ),
    "planDeletedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Plan deleted successfully",
    ),
    "planDetails": MessageLookupByLibrary.simpleMessage("Plan Details"),
    "planInfo": MessageLookupByLibrary.simpleMessage("Plan Information"),
    "planStatus": MessageLookupByLibrary.simpleMessage("Plan Status"),
    "planUpdateFailed": MessageLookupByLibrary.simpleMessage(
      "Failed to update plan",
    ),
    "planUpdatedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Plan updated successfully",
    ),
    "planned": MessageLookupByLibrary.simpleMessage("Planned"),
    "plans": MessageLookupByLibrary.simpleMessage("Plans"),
    "pleaseAuthenticateToAddSignature": MessageLookupByLibrary.simpleMessage(
      "Please authenticate to add signature",
    ),
    "pleaseCheckYourCredentials": MessageLookupByLibrary.simpleMessage(
      "Please check your credentials",
    ),
    "recentActiveTickets": MessageLookupByLibrary.simpleMessage(
      "Recent Active Tickets",
    ),
    "register": MessageLookupByLibrary.simpleMessage("Register"),
    "registrationFailed": MessageLookupByLibrary.simpleMessage(
      "Registration failed",
    ),
    "registrationSuccessful": MessageLookupByLibrary.simpleMessage(
      "Registration successful",
    ),
    "regularSignature": MessageLookupByLibrary.simpleMessage(
      "Regular Signature",
    ),
    "regularSignatures": MessageLookupByLibrary.simpleMessage(
      "Regular Signatures",
    ),
    "remainingMaintenance": MessageLookupByLibrary.simpleMessage(
      "Remaining Maintenance",
    ),
    "repliedOnTheTicket": MessageLookupByLibrary.simpleMessage(
      "Replied on the ticket",
    ),
    "replies": MessageLookupByLibrary.simpleMessage("Replies"),
    "reply": MessageLookupByLibrary.simpleMessage("Reply"),
    "replyCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
      "Reply cannot be empty",
    ),
    "replySentSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Reply sent successfully",
    ),
    "reports": MessageLookupByLibrary.simpleMessage("Reports"),
    "request": MessageLookupByLibrary.simpleMessage("Request"),
    "requestLeaves": MessageLookupByLibrary.simpleMessage("Request Leaves"),
    "retry": MessageLookupByLibrary.simpleMessage("Retry"),
    "save": MessageLookupByLibrary.simpleMessage("Save"),
    "search": MessageLookupByLibrary.simpleMessage("Search"),
    "searchClients": MessageLookupByLibrary.simpleMessage("Search clients..."),
    "selectClient": MessageLookupByLibrary.simpleMessage("Select Client"),
    "selectDate": MessageLookupByLibrary.simpleMessage("Select Date"),
    "selectDelivery": MessageLookupByLibrary.simpleMessage("Select Delivery"),
    "settings": MessageLookupByLibrary.simpleMessage("Settings"),
    "showOnMap": MessageLookupByLibrary.simpleMessage("Show on Map"),
    "sickLeaves": MessageLookupByLibrary.simpleMessage("Sick Leaves"),
    "signUp": MessageLookupByLibrary.simpleMessage("Sign Up"),
    "signature": MessageLookupByLibrary.simpleMessage("Signature"),
    "signatureAddFailed": MessageLookupByLibrary.simpleMessage(
      "Failed to add signature",
    ),
    "signatureAddedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Signature added successfully",
    ),
    "signatureImage": MessageLookupByLibrary.simpleMessage("Signature Image"),
    "signatureLocation": MessageLookupByLibrary.simpleMessage(
      "Signature Location",
    ),
    "signaturePlace": MessageLookupByLibrary.simpleMessage("Signature Place"),
    "signatureTime": MessageLookupByLibrary.simpleMessage("Signature Time"),
    "signatures": MessageLookupByLibrary.simpleMessage("Signatures"),
    "somethingWentWrong": MessageLookupByLibrary.simpleMessage(
      "Something went wrong",
    ),
    "sortByDate": MessageLookupByLibrary.simpleMessage("Sort by Date"),
    "startDate": MessageLookupByLibrary.simpleMessage("Start Date"),
    "startTime": MessageLookupByLibrary.simpleMessage("Start Time"),
    "statistics": MessageLookupByLibrary.simpleMessage("Statistics"),
    "status": MessageLookupByLibrary.simpleMessage("Status"),
    "submit": MessageLookupByLibrary.simpleMessage("Submit"),
    "summary": MessageLookupByLibrary.simpleMessage("Summary"),
    "systemSetting": MessageLookupByLibrary.simpleMessage("System Setting"),
    "theme": MessageLookupByLibrary.simpleMessage("Theme"),
    "timeRange": MessageLookupByLibrary.simpleMessage("Time Range"),
    "timeline": MessageLookupByLibrary.simpleMessage("Timeline"),
    "toDate": MessageLookupByLibrary.simpleMessage("To Date"),
    "todaySignatures": MessageLookupByLibrary.simpleMessage(
      "Today\'s Signatures",
    ),
    "totalAttendances": MessageLookupByLibrary.simpleMessage(
      "Total Attendances",
    ),
    "totalAttended": MessageLookupByLibrary.simpleMessage("Total Attended"),
    "totalMissed": MessageLookupByLibrary.simpleMessage("Total Missed"),
    "totalPlanned": MessageLookupByLibrary.simpleMessage("Total Planned"),
    "totalPlans": MessageLookupByLibrary.simpleMessage("Total Plans"),
    "totalSignatures": MessageLookupByLibrary.simpleMessage("Total Signatures"),
    "totalTickets": MessageLookupByLibrary.simpleMessage("Total Tickets"),
    "updateClient": MessageLookupByLibrary.simpleMessage("Update Client"),
    "userNotFound": MessageLookupByLibrary.simpleMessage("User not found"),
    "username": MessageLookupByLibrary.simpleMessage("Username"),
    "viewSignature": MessageLookupByLibrary.simpleMessage("View Signature"),
    "weakPassword": MessageLookupByLibrary.simpleMessage(
      "Password is too weak",
    ),
    "welcomeBack": MessageLookupByLibrary.simpleMessage("Welcome back"),
    "welcomeBackLine": MessageLookupByLibrary.simpleMessage("Welcome\nback"),
    "welcomeWithName": m1,
  };
}
