// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ar locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ar';

  static String m0(name) => "مرحبًا، ${name}";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "absents": MessageLookupByLibrary.simpleMessage("غياب"),
    "active": MessageLookupByLibrary.simpleMessage("نشط"),
    "activeTasks": MessageLookupByLibrary.simpleMessage("المهام النشطة"),
    "addNewTicket": MessageLookupByLibrary.simpleMessage("إضافة تذكرة جديدة"),
    "addSignature": MessageLookupByLibrary.simpleMessage("إضافة توقيع"),
    "admin": MessageLookupByLibrary.simpleMessage("مدير"),
    "allTickets": MessageLookupByLibrary.simpleMessage("جميع التذاكر"),
    "alreadyHaveAccount": MessageLookupByLibrary.simpleMessage(
      "لديك حساب بالفعل؟",
    ),
    "arabic": MessageLookupByLibrary.simpleMessage("العربية"),
    "archived": MessageLookupByLibrary.simpleMessage("مؤرشف"),
    "areYouSureLogout": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد أنك تريد تسجيل الخروج؟",
    ),
    "areYouSureYouWantToLogout": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد أنك تريد تسجيل الخروج؟",
    ),
    "ascending": MessageLookupByLibrary.simpleMessage("تصاعدي"),
    "attachment": MessageLookupByLibrary.simpleMessage("المرفق"),
    "attendanceTime": MessageLookupByLibrary.simpleMessage("وقت الحضور"),
    "authenticate": MessageLookupByLibrary.simpleMessage("المصادقة"),
    "authenticationRequired": MessageLookupByLibrary.simpleMessage(
      "المصادقة مطلوبة",
    ),
    "biometricNotAvailable": MessageLookupByLibrary.simpleMessage(
      "المصادقة البيومترية غير متاحة",
    ),
    "camera": MessageLookupByLibrary.simpleMessage("الكاميرا"),
    "cancel": MessageLookupByLibrary.simpleMessage("إلغاء"),
    "changeLanguage": MessageLookupByLibrary.simpleMessage("تغيير اللغة"),
    "checkIn": MessageLookupByLibrary.simpleMessage("تسجيل الحضور"),
    "completeAttends": MessageLookupByLibrary.simpleMessage("حضور كامل"),
    "confirm": MessageLookupByLibrary.simpleMessage("تأكيد"),
    "coordinates": MessageLookupByLibrary.simpleMessage("الإحداثيات"),
    "createAccount": MessageLookupByLibrary.simpleMessage("إنشاء حساب"),
    "currentTime": MessageLookupByLibrary.simpleMessage("الوقت الحالي"),
    "dark": MessageLookupByLibrary.simpleMessage("داكن"),
    "dateRange": MessageLookupByLibrary.simpleMessage("نطاق التاريخ"),
    "days": MessageLookupByLibrary.simpleMessage("أيام"),
    "descending": MessageLookupByLibrary.simpleMessage("تنازلي"),
    "description": MessageLookupByLibrary.simpleMessage("الوصف"),
    "dontHaveAccount": MessageLookupByLibrary.simpleMessage("ليس لديك حساب؟"),
    "email": MessageLookupByLibrary.simpleMessage("البريد الإلكتروني"),
    "emailAlreadyInUse": MessageLookupByLibrary.simpleMessage(
      "البريد الإلكتروني مستخدم بالفعل",
    ),
    "endDate": MessageLookupByLibrary.simpleMessage("تاريخ النهاية"),
    "english": MessageLookupByLibrary.simpleMessage("English"),
    "enter": MessageLookupByLibrary.simpleMessage("أدخل"),
    "enterPlace": MessageLookupByLibrary.simpleMessage("أدخل المكان"),
    "export": MessageLookupByLibrary.simpleMessage("تصدير"),
    "exportExcel": MessageLookupByLibrary.simpleMessage("تصدير Excel"),
    "exportPdf": MessageLookupByLibrary.simpleMessage("تصدير PDF"),
    "fingerprint": MessageLookupByLibrary.simpleMessage("بصمة الإصبع"),
    "finishedTasks": MessageLookupByLibrary.simpleMessage("المهام المنجزة"),
    "firstOfDay": MessageLookupByLibrary.simpleMessage("أول اليوم"),
    "fromDate": MessageLookupByLibrary.simpleMessage("من تاريخ"),
    "gallery": MessageLookupByLibrary.simpleMessage("المعرض"),
    "home": MessageLookupByLibrary.simpleMessage("الرئيسية"),
    "incompleteAttends": MessageLookupByLibrary.simpleMessage("حضور ناقص"),
    "invalidCredentials": MessageLookupByLibrary.simpleMessage(
      "بيانات اعتماد غير صحيحة",
    ),
    "issue": MessageLookupByLibrary.simpleMessage("مشكلة"),
    "issuerEmail": MessageLookupByLibrary.simpleMessage("البريد الإلكتروني"),
    "issuerName": MessageLookupByLibrary.simpleMessage("اسم المنشئ"),
    "issuerPhone": MessageLookupByLibrary.simpleMessage("الهاتف"),
    "itsGreatToSeeYou": MessageLookupByLibrary.simpleMessage("من الرائع رؤيتك"),
    "language": MessageLookupByLibrary.simpleMessage("اللغة"),
    "light": MessageLookupByLibrary.simpleMessage("فاتح"),
    "location": MessageLookupByLibrary.simpleMessage("الموقع"),
    "locationPermissionDenied": MessageLookupByLibrary.simpleMessage(
      "تم رفض إذن الموقع",
    ),
    "locationRequired": MessageLookupByLibrary.simpleMessage("الموقع مطلوب"),
    "login": MessageLookupByLibrary.simpleMessage("تسجيل الدخول"),
    "loginFailed": MessageLookupByLibrary.simpleMessage("فشل تسجيل الدخول"),
    "logout": MessageLookupByLibrary.simpleMessage("تسجيل الخروج"),
    "logoutConfirmation": MessageLookupByLibrary.simpleMessage(
      "تأكيد تسجيل الخروج",
    ),
    "mandob": MessageLookupByLibrary.simpleMessage("مندوب"),
    "monthlyStats": MessageLookupByLibrary.simpleMessage("إحصائيات الشهر"),
    "mySubscriptions": MessageLookupByLibrary.simpleMessage("اشتراكاتي"),
    "name": MessageLookupByLibrary.simpleMessage("الاسم"),
    "networkError": MessageLookupByLibrary.simpleMessage("خطأ في الشبكة"),
    "newReplyOnTicket": MessageLookupByLibrary.simpleMessage(
      "رد جديد على التذكرة",
    ),
    "noDataAvailable": MessageLookupByLibrary.simpleMessage(
      "لا توجد بيانات متاحة",
    ),
    "noDataFound": MessageLookupByLibrary.simpleMessage("لا توجد بيانات"),
    "noRepliesFound": MessageLookupByLibrary.simpleMessage(
      "لم يتم العثور على ردود",
    ),
    "noSignaturesToday": MessageLookupByLibrary.simpleMessage(
      "لا توجد توقيعات اليوم",
    ),
    "office": MessageLookupByLibrary.simpleMessage("المكتب"),
    "officeSignature": MessageLookupByLibrary.simpleMessage("توقيع المكتب"),
    "officeSignatures": MessageLookupByLibrary.simpleMessage("توقيعات المكتب"),
    "officialHolidays": MessageLookupByLibrary.simpleMessage("عطل رسمية"),
    "password": MessageLookupByLibrary.simpleMessage("كلمة المرور"),
    "pickImage": MessageLookupByLibrary.simpleMessage("اختر صورة"),
    "place": MessageLookupByLibrary.simpleMessage("المكان"),
    "pleaseAuthenticateToAddSignature": MessageLookupByLibrary.simpleMessage(
      "يرجى المصادقة لإضافة التوقيع",
    ),
    "pleaseCheckYourCredentials": MessageLookupByLibrary.simpleMessage(
      "يرجى التحقق من بيانات الدخول الخاصة بك",
    ),
    "recentActiveTickets": MessageLookupByLibrary.simpleMessage(
      "التذاكر النشطة الأخيرة",
    ),
    "register": MessageLookupByLibrary.simpleMessage("تسجيل"),
    "registrationFailed": MessageLookupByLibrary.simpleMessage("فشل التسجيل"),
    "registrationSuccessful": MessageLookupByLibrary.simpleMessage(
      "تم التسجيل بنجاح",
    ),
    "regularSignature": MessageLookupByLibrary.simpleMessage("توقيع عادي"),
    "regularSignatures": MessageLookupByLibrary.simpleMessage(
      "التوقيعات العادية",
    ),
    "remainingMaintenance": MessageLookupByLibrary.simpleMessage(
      "الصيانة المتبقية",
    ),
    "repliedOnTheTicket": MessageLookupByLibrary.simpleMessage(
      "رد على التذكرة",
    ),
    "replies": MessageLookupByLibrary.simpleMessage("الردود"),
    "reply": MessageLookupByLibrary.simpleMessage("رد"),
    "replyCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
      "لا يمكن أن يكون الرد فارغًا",
    ),
    "replySentSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم إرسال الرد بنجاح",
    ),
    "reports": MessageLookupByLibrary.simpleMessage("التقارير"),
    "request": MessageLookupByLibrary.simpleMessage("طلب"),
    "requestLeaves": MessageLookupByLibrary.simpleMessage("إجازات مطلوبة"),
    "save": MessageLookupByLibrary.simpleMessage("حفظ"),
    "search": MessageLookupByLibrary.simpleMessage("بحث"),
    "selectDate": MessageLookupByLibrary.simpleMessage("اختر تاريخ"),
    "selectDelivery": MessageLookupByLibrary.simpleMessage("اختر المندوب"),
    "settings": MessageLookupByLibrary.simpleMessage("الإعدادات"),
    "showOnMap": MessageLookupByLibrary.simpleMessage("عرض على الخريطة"),
    "sickLeaves": MessageLookupByLibrary.simpleMessage("إجازات مرضية"),
    "signUp": MessageLookupByLibrary.simpleMessage("التسجيل"),
    "signature": MessageLookupByLibrary.simpleMessage("التوقيع"),
    "signatureAddFailed": MessageLookupByLibrary.simpleMessage(
      "فشل في إضافة التوقيع",
    ),
    "signatureAddedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم إضافة التوقيع بنجاح",
    ),
    "signatureLocation": MessageLookupByLibrary.simpleMessage("موقع التوقيع"),
    "signaturePlace": MessageLookupByLibrary.simpleMessage("مكان التوقيع"),
    "signatureTime": MessageLookupByLibrary.simpleMessage("وقت التوقيع"),
    "signatures": MessageLookupByLibrary.simpleMessage("التوقيعات"),
    "somethingWentWrong": MessageLookupByLibrary.simpleMessage("حدث خطأ ما"),
    "sortByDate": MessageLookupByLibrary.simpleMessage("ترتيب حسب التاريخ"),
    "startDate": MessageLookupByLibrary.simpleMessage("تاريخ البداية"),
    "status": MessageLookupByLibrary.simpleMessage("الحالة"),
    "submit": MessageLookupByLibrary.simpleMessage("إرسال"),
    "summary": MessageLookupByLibrary.simpleMessage("ملخص"),
    "systemSetting": MessageLookupByLibrary.simpleMessage("إعداد النظام"),
    "theme": MessageLookupByLibrary.simpleMessage("المظهر"),
    "toDate": MessageLookupByLibrary.simpleMessage("إلى تاريخ"),
    "todaySignatures": MessageLookupByLibrary.simpleMessage("توقيعات اليوم"),
    "totalSignatures": MessageLookupByLibrary.simpleMessage("إجمالي التوقيعات"),
    "totalTickets": MessageLookupByLibrary.simpleMessage("إجمالي التذاكر"),
    "userNotFound": MessageLookupByLibrary.simpleMessage("المستخدم غير موجود"),
    "username": MessageLookupByLibrary.simpleMessage("اسم المستخدم"),
    "weakPassword": MessageLookupByLibrary.simpleMessage(
      "كلمة المرور ضعيفة جداً",
    ),
    "welcomeBack": MessageLookupByLibrary.simpleMessage("مرحبًا بعودتك"),
    "welcomeBackLine": MessageLookupByLibrary.simpleMessage("مرحبًا\nبعودتك"),
    "welcomeWithName": m0,
  };
}
