// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ar locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ar';

  static String m0(name) => "هل أنت متأكد من حذف ${name}؟";

  static String m1(name) => "مرحبًا، ${name}";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "absents": MessageLookupByLibrary.simpleMessage("غياب"),
    "active": MessageLookupByLibrary.simpleMessage("نشط"),
    "activeTasks": MessageLookupByLibrary.simpleMessage("المهام النشطة"),
    "addClient": MessageLookupByLibrary.simpleMessage("إضافة عميل"),
    "addFirstClient": MessageLookupByLibrary.simpleMessage("إضافة أول عميل"),
    "addFirstPlan": MessageLookupByLibrary.simpleMessage("إضافة أول خطة"),
    "addNewTicket": MessageLookupByLibrary.simpleMessage("إضافة تذكرة جديدة"),
    "addPlan": MessageLookupByLibrary.simpleMessage("إضافة خطة"),
    "addSignature": MessageLookupByLibrary.simpleMessage("إضافة توقيع"),
    "address": MessageLookupByLibrary.simpleMessage("العنوان"),
    "admin": MessageLookupByLibrary.simpleMessage("مدير"),
    "allTickets": MessageLookupByLibrary.simpleMessage("جميع التذاكر"),
    "alreadyHaveAccount": MessageLookupByLibrary.simpleMessage(
      "لديك حساب بالفعل؟",
    ),
    "arabic": MessageLookupByLibrary.simpleMessage("العربية"),
    "archived": MessageLookupByLibrary.simpleMessage("مؤرشف"),
    "areYouSureLogout": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد أنك تريد تسجيل الخروج؟",
    ),
    "areYouSureYouWantToLogout": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد أنك تريد تسجيل الخروج؟",
    ),
    "ascending": MessageLookupByLibrary.simpleMessage("تصاعدي"),
    "attachToPlan": MessageLookupByLibrary.simpleMessage("ربط بالخطة"),
    "attachToPlanDescription": MessageLookupByLibrary.simpleMessage(
      "إنشاء أو تحديث خطة تلقائياً لهذا العميل",
    ),
    "attachment": MessageLookupByLibrary.simpleMessage("المرفق"),
    "attendanceDetails": MessageLookupByLibrary.simpleMessage("تفاصيل الحضور"),
    "attendanceRate": MessageLookupByLibrary.simpleMessage("معدل الحضور"),
    "attendanceTime": MessageLookupByLibrary.simpleMessage("وقت الحضور"),
    "attendances": MessageLookupByLibrary.simpleMessage("الحضور"),
    "attended": MessageLookupByLibrary.simpleMessage("تم الحضور"),
    "authenticate": MessageLookupByLibrary.simpleMessage("المصادقة"),
    "authenticationRequired": MessageLookupByLibrary.simpleMessage(
      "المصادقة مطلوبة",
    ),
    "biometricNotAvailable": MessageLookupByLibrary.simpleMessage(
      "المصادقة البيومترية غير متاحة",
    ),
    "camera": MessageLookupByLibrary.simpleMessage("الكاميرا"),
    "cancel": MessageLookupByLibrary.simpleMessage("إلغاء"),
    "cannotOpenMap": MessageLookupByLibrary.simpleMessage(
      "لا يمكن فتح الخريطة",
    ),
    "changeLanguage": MessageLookupByLibrary.simpleMessage("تغيير اللغة"),
    "checkIn": MessageLookupByLibrary.simpleMessage("تسجيل الحضور"),
    "client": MessageLookupByLibrary.simpleMessage("عميل"),
    "clientAddFailed": MessageLookupByLibrary.simpleMessage(
      "فشل في إضافة العميل",
    ),
    "clientAddedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم إضافة العميل بنجاح",
    ),
    "clientAddress": MessageLookupByLibrary.simpleMessage("عنوان العميل"),
    "clientAddressRequired": MessageLookupByLibrary.simpleMessage(
      "عنوان العميل مطلوب",
    ),
    "clientDeleteFailed": MessageLookupByLibrary.simpleMessage(
      "فشل في حذف العميل",
    ),
    "clientDeletedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم حذف العميل بنجاح",
    ),
    "clientInfo": MessageLookupByLibrary.simpleMessage("معلومات العميل"),
    "clientName": MessageLookupByLibrary.simpleMessage("اسم العميل"),
    "clientNameRequired": MessageLookupByLibrary.simpleMessage(
      "اسم العميل مطلوب",
    ),
    "clientType": MessageLookupByLibrary.simpleMessage("نوع العميل"),
    "clientUpdateFailed": MessageLookupByLibrary.simpleMessage(
      "فشل في تحديث العميل",
    ),
    "clientUpdatedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم تحديث العميل بنجاح",
    ),
    "clients": MessageLookupByLibrary.simpleMessage("العملاء"),
    "close": MessageLookupByLibrary.simpleMessage("إغلاق"),
    "completeAttends": MessageLookupByLibrary.simpleMessage("حضور كامل"),
    "confirm": MessageLookupByLibrary.simpleMessage("تأكيد"),
    "coordinates": MessageLookupByLibrary.simpleMessage("الإحداثيات"),
    "createAccount": MessageLookupByLibrary.simpleMessage("إنشاء حساب"),
    "currentTime": MessageLookupByLibrary.simpleMessage("الوقت الحالي"),
    "dailyTimeline": MessageLookupByLibrary.simpleMessage(
      "الجدول الزمني اليومي",
    ),
    "dark": MessageLookupByLibrary.simpleMessage("داكن"),
    "dateRange": MessageLookupByLibrary.simpleMessage("نطاق التاريخ"),
    "days": MessageLookupByLibrary.simpleMessage("أيام"),
    "deal": MessageLookupByLibrary.simpleMessage("صفقة"),
    "delete": MessageLookupByLibrary.simpleMessage("حذف"),
    "deleteClient": MessageLookupByLibrary.simpleMessage("حذف عميل"),
    "deleteClientConfirmation": m0,
    "deletePlan": MessageLookupByLibrary.simpleMessage("حذف خطة"),
    "deletePlanConfirmation": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من حذف هذه الخطة؟",
    ),
    "descending": MessageLookupByLibrary.simpleMessage("تنازلي"),
    "description": MessageLookupByLibrary.simpleMessage("الوصف"),
    "dontHaveAccount": MessageLookupByLibrary.simpleMessage("ليس لديك حساب؟"),
    "edit": MessageLookupByLibrary.simpleMessage("تعديل"),
    "editClient": MessageLookupByLibrary.simpleMessage("تعديل عميل"),
    "editPlan": MessageLookupByLibrary.simpleMessage("تعديل خطة"),
    "email": MessageLookupByLibrary.simpleMessage("البريد الإلكتروني"),
    "emailAlreadyInUse": MessageLookupByLibrary.simpleMessage(
      "البريد الإلكتروني مستخدم بالفعل",
    ),
    "endDate": MessageLookupByLibrary.simpleMessage("تاريخ النهاية"),
    "endTime": MessageLookupByLibrary.simpleMessage("وقت النهاية"),
    "endTimeMustBeAfterStartTime": MessageLookupByLibrary.simpleMessage(
      "وقت النهاية يجب أن يكون بعد وقت البداية",
    ),
    "english": MessageLookupByLibrary.simpleMessage("English"),
    "enter": MessageLookupByLibrary.simpleMessage("أدخل"),
    "enterAddress": MessageLookupByLibrary.simpleMessage("أدخل العنوان"),
    "enterClientName": MessageLookupByLibrary.simpleMessage("أدخل اسم العميل"),
    "enterNote": MessageLookupByLibrary.simpleMessage("أدخل ملاحظة"),
    "enterPlace": MessageLookupByLibrary.simpleMessage("أدخل المكان"),
    "errorLoadingClients": MessageLookupByLibrary.simpleMessage(
      "خطأ في تحميل العملاء",
    ),
    "export": MessageLookupByLibrary.simpleMessage("تصدير"),
    "exportExcel": MessageLookupByLibrary.simpleMessage("تصدير Excel"),
    "exportPdf": MessageLookupByLibrary.simpleMessage("تصدير PDF"),
    "fingerprint": MessageLookupByLibrary.simpleMessage("بصمة الإصبع"),
    "finishedTasks": MessageLookupByLibrary.simpleMessage("المهام المنجزة"),
    "firstOfDay": MessageLookupByLibrary.simpleMessage("أول اليوم"),
    "fromDate": MessageLookupByLibrary.simpleMessage("من تاريخ"),
    "gallery": MessageLookupByLibrary.simpleMessage("المعرض"),
    "getLocation": MessageLookupByLibrary.simpleMessage("الحصول على الموقع"),
    "home": MessageLookupByLibrary.simpleMessage("الرئيسية"),
    "incompleteAttends": MessageLookupByLibrary.simpleMessage("حضور ناقص"),
    "invalidCredentials": MessageLookupByLibrary.simpleMessage(
      "بيانات اعتماد غير صحيحة",
    ),
    "issue": MessageLookupByLibrary.simpleMessage("مشكلة"),
    "issuerEmail": MessageLookupByLibrary.simpleMessage("البريد الإلكتروني"),
    "issuerName": MessageLookupByLibrary.simpleMessage("اسم المنشئ"),
    "issuerPhone": MessageLookupByLibrary.simpleMessage("الهاتف"),
    "itsGreatToSeeYou": MessageLookupByLibrary.simpleMessage("من الرائع رؤيتك"),
    "language": MessageLookupByLibrary.simpleMessage("اللغة"),
    "lead": MessageLookupByLibrary.simpleMessage("عميل محتمل"),
    "light": MessageLookupByLibrary.simpleMessage("فاتح"),
    "location": MessageLookupByLibrary.simpleMessage("الموقع"),
    "locationObtained": MessageLookupByLibrary.simpleMessage(
      "تم الحصول على الموقع بنجاح",
    ),
    "locationPermissionDenied": MessageLookupByLibrary.simpleMessage(
      "تم رفض إذن الموقع",
    ),
    "locationRequired": MessageLookupByLibrary.simpleMessage("الموقع مطلوب"),
    "login": MessageLookupByLibrary.simpleMessage("تسجيل الدخول"),
    "loginFailed": MessageLookupByLibrary.simpleMessage("فشل تسجيل الدخول"),
    "logout": MessageLookupByLibrary.simpleMessage("تسجيل الخروج"),
    "logoutConfirmation": MessageLookupByLibrary.simpleMessage(
      "تأكيد تسجيل الخروج",
    ),
    "mandob": MessageLookupByLibrary.simpleMessage("مندوب"),
    "migration": MessageLookupByLibrary.simpleMessage("الترحيل"),
    "missed": MessageLookupByLibrary.simpleMessage("فائت"),
    "monthlyStats": MessageLookupByLibrary.simpleMessage("إحصائيات الشهر"),
    "mySubscriptions": MessageLookupByLibrary.simpleMessage("اشتراكاتي"),
    "name": MessageLookupByLibrary.simpleMessage("الاسم"),
    "networkError": MessageLookupByLibrary.simpleMessage("خطأ في الشبكة"),
    "newReplyOnTicket": MessageLookupByLibrary.simpleMessage(
      "رد جديد على التذكرة",
    ),
    "noAttendances": MessageLookupByLibrary.simpleMessage("لا يوجد حضور"),
    "noClientsFound": MessageLookupByLibrary.simpleMessage(
      "لم يتم العثور على عملاء",
    ),
    "noClientsMatchSearch": MessageLookupByLibrary.simpleMessage(
      "لا يوجد عملاء يطابقون البحث",
    ),
    "noDataAvailable": MessageLookupByLibrary.simpleMessage(
      "لا توجد بيانات متاحة",
    ),
    "noDataFound": MessageLookupByLibrary.simpleMessage("لا توجد بيانات"),
    "noPlansFound": MessageLookupByLibrary.simpleMessage(
      "لم يتم العثور على خطط",
    ),
    "noPlansTodayFound": MessageLookupByLibrary.simpleMessage(
      "لم يتم العثور على خطط لليوم",
    ),
    "noRepliesFound": MessageLookupByLibrary.simpleMessage(
      "لم يتم العثور على ردود",
    ),
    "noSignaturesToday": MessageLookupByLibrary.simpleMessage(
      "لا توجد توقيعات اليوم",
    ),
    "note": MessageLookupByLibrary.simpleMessage("ملاحظة"),
    "office": MessageLookupByLibrary.simpleMessage("المكتب"),
    "officeSignature": MessageLookupByLibrary.simpleMessage("توقيع المكتب"),
    "officeSignatures": MessageLookupByLibrary.simpleMessage("توقيعات المكتب"),
    "officialHolidays": MessageLookupByLibrary.simpleMessage("عطل رسمية"),
    "password": MessageLookupByLibrary.simpleMessage("كلمة المرور"),
    "pickImage": MessageLookupByLibrary.simpleMessage("اختر صورة"),
    "place": MessageLookupByLibrary.simpleMessage("المكان"),
    "plan": MessageLookupByLibrary.simpleMessage("خطة"),
    "planAddFailed": MessageLookupByLibrary.simpleMessage("فشل في إضافة الخطة"),
    "planAddedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم إضافة الخطة بنجاح",
    ),
    "planDate": MessageLookupByLibrary.simpleMessage("تاريخ الخطة"),
    "planDeleteFailed": MessageLookupByLibrary.simpleMessage(
      "فشل في حذف الخطة",
    ),
    "planDeletedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم حذف الخطة بنجاح",
    ),
    "planDetails": MessageLookupByLibrary.simpleMessage("تفاصيل الخطة"),
    "planInfo": MessageLookupByLibrary.simpleMessage("معلومات الخطة"),
    "planStatus": MessageLookupByLibrary.simpleMessage("حالة الخطة"),
    "planUpdateFailed": MessageLookupByLibrary.simpleMessage(
      "فشل في تحديث الخطة",
    ),
    "planUpdatedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم تحديث الخطة بنجاح",
    ),
    "planned": MessageLookupByLibrary.simpleMessage("مخطط"),
    "plans": MessageLookupByLibrary.simpleMessage("الخطط"),
    "pleaseAuthenticateToAddSignature": MessageLookupByLibrary.simpleMessage(
      "يرجى المصادقة لإضافة التوقيع",
    ),
    "pleaseCheckYourCredentials": MessageLookupByLibrary.simpleMessage(
      "يرجى التحقق من بيانات الدخول الخاصة بك",
    ),
    "recentActiveTickets": MessageLookupByLibrary.simpleMessage(
      "التذاكر النشطة الأخيرة",
    ),
    "register": MessageLookupByLibrary.simpleMessage("تسجيل"),
    "registrationFailed": MessageLookupByLibrary.simpleMessage("فشل التسجيل"),
    "registrationSuccessful": MessageLookupByLibrary.simpleMessage(
      "تم التسجيل بنجاح",
    ),
    "regularSignature": MessageLookupByLibrary.simpleMessage("توقيع عادي"),
    "regularSignatures": MessageLookupByLibrary.simpleMessage(
      "التوقيعات العادية",
    ),
    "remainingMaintenance": MessageLookupByLibrary.simpleMessage(
      "الصيانة المتبقية",
    ),
    "repliedOnTheTicket": MessageLookupByLibrary.simpleMessage(
      "رد على التذكرة",
    ),
    "replies": MessageLookupByLibrary.simpleMessage("الردود"),
    "reply": MessageLookupByLibrary.simpleMessage("رد"),
    "replyCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
      "لا يمكن أن يكون الرد فارغًا",
    ),
    "replySentSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم إرسال الرد بنجاح",
    ),
    "reports": MessageLookupByLibrary.simpleMessage("التقارير"),
    "request": MessageLookupByLibrary.simpleMessage("طلب"),
    "requestLeaves": MessageLookupByLibrary.simpleMessage("إجازات مطلوبة"),
    "retry": MessageLookupByLibrary.simpleMessage("إعادة المحاولة"),
    "save": MessageLookupByLibrary.simpleMessage("حفظ"),
    "search": MessageLookupByLibrary.simpleMessage("بحث"),
    "searchClients": MessageLookupByLibrary.simpleMessage(
      "البحث عن العملاء...",
    ),
    "selectClient": MessageLookupByLibrary.simpleMessage("اختر عميل"),
    "selectDate": MessageLookupByLibrary.simpleMessage("اختر تاريخ"),
    "selectDelivery": MessageLookupByLibrary.simpleMessage("اختر المندوب"),
    "settings": MessageLookupByLibrary.simpleMessage("الإعدادات"),
    "showOnMap": MessageLookupByLibrary.simpleMessage("عرض على الخريطة"),
    "sickLeaves": MessageLookupByLibrary.simpleMessage("إجازات مرضية"),
    "signUp": MessageLookupByLibrary.simpleMessage("التسجيل"),
    "signature": MessageLookupByLibrary.simpleMessage("التوقيع"),
    "signatureAddFailed": MessageLookupByLibrary.simpleMessage(
      "فشل في إضافة التوقيع",
    ),
    "signatureAddedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم إضافة التوقيع بنجاح",
    ),
    "signatureImage": MessageLookupByLibrary.simpleMessage("صورة التوقيع"),
    "signatureLocation": MessageLookupByLibrary.simpleMessage("موقع التوقيع"),
    "signaturePlace": MessageLookupByLibrary.simpleMessage("مكان التوقيع"),
    "signatureTime": MessageLookupByLibrary.simpleMessage("وقت التوقيع"),
    "signatures": MessageLookupByLibrary.simpleMessage("التوقيعات"),
    "somethingWentWrong": MessageLookupByLibrary.simpleMessage("حدث خطأ ما"),
    "sortByDate": MessageLookupByLibrary.simpleMessage("ترتيب حسب التاريخ"),
    "startDate": MessageLookupByLibrary.simpleMessage("تاريخ البداية"),
    "startTime": MessageLookupByLibrary.simpleMessage("وقت البداية"),
    "statistics": MessageLookupByLibrary.simpleMessage("الإحصائيات"),
    "status": MessageLookupByLibrary.simpleMessage("الحالة"),
    "submit": MessageLookupByLibrary.simpleMessage("إرسال"),
    "summary": MessageLookupByLibrary.simpleMessage("ملخص"),
    "systemSetting": MessageLookupByLibrary.simpleMessage("إعداد النظام"),
    "theme": MessageLookupByLibrary.simpleMessage("المظهر"),
    "timeRange": MessageLookupByLibrary.simpleMessage("نطاق الوقت"),
    "timeline": MessageLookupByLibrary.simpleMessage("الجدول الزمني"),
    "toDate": MessageLookupByLibrary.simpleMessage("إلى تاريخ"),
    "todaySignatures": MessageLookupByLibrary.simpleMessage("توقيعات اليوم"),
    "totalAttendances": MessageLookupByLibrary.simpleMessage("إجمالي الحضور"),
    "totalAttended": MessageLookupByLibrary.simpleMessage("إجمالي الحضور"),
    "totalMissed": MessageLookupByLibrary.simpleMessage("إجمالي الفائت"),
    "totalPlanned": MessageLookupByLibrary.simpleMessage("إجمالي المخطط"),
    "totalPlans": MessageLookupByLibrary.simpleMessage("إجمالي الخطط"),
    "totalSignatures": MessageLookupByLibrary.simpleMessage("إجمالي التوقيعات"),
    "totalTickets": MessageLookupByLibrary.simpleMessage("إجمالي التذاكر"),
    "updateClient": MessageLookupByLibrary.simpleMessage("تحديث العميل"),
    "userNotFound": MessageLookupByLibrary.simpleMessage("المستخدم غير موجود"),
    "username": MessageLookupByLibrary.simpleMessage("اسم المستخدم"),
    "viewSignature": MessageLookupByLibrary.simpleMessage("عرض التوقيع"),
    "weakPassword": MessageLookupByLibrary.simpleMessage(
      "كلمة المرور ضعيفة جداً",
    ),
    "welcomeBack": MessageLookupByLibrary.simpleMessage("مرحبًا بعودتك"),
    "welcomeBackLine": MessageLookupByLibrary.simpleMessage("مرحبًا\nبعودتك"),
    "welcomeWithName": m1,
  };
}
