// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(
      _current != null,
      'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.',
    );
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(
      instance != null,
      'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?',
    );
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `No data found`
  String get noDataFound {
    return Intl.message(
      'No data found',
      name: 'noDataFound',
      desc: '',
      args: [],
    );
  }

  /// `Enter`
  String get enter {
    return Intl.message('Enter', name: 'enter', desc: '', args: []);
  }

  /// `Pick Image`
  String get pickImage {
    return Intl.message('Pick Image', name: 'pickImage', desc: '', args: []);
  }

  /// `Search`
  String get search {
    return Intl.message('Search', name: 'search', desc: '', args: []);
  }

  /// `Welcome, {name}`
  String welcomeWithName(Object name) {
    return Intl.message(
      'Welcome, $name',
      name: 'welcomeWithName',
      desc: '',
      args: [name],
    );
  }

  /// `Home`
  String get home {
    return Intl.message('Home', name: 'home', desc: '', args: []);
  }

  /// `Are you sure you want to logout?`
  String get areYouSureLogout {
    return Intl.message(
      'Are you sure you want to logout?',
      name: 'areYouSureLogout',
      desc: '',
      args: [],
    );
  }

  /// `Logout Confirmation`
  String get logoutConfirmation {
    return Intl.message(
      'Logout Confirmation',
      name: 'logoutConfirmation',
      desc: '',
      args: [],
    );
  }

  /// `Register`
  String get register {
    return Intl.message('Register', name: 'register', desc: '', args: []);
  }

  /// `Login`
  String get login {
    return Intl.message('Login', name: 'login', desc: '', args: []);
  }

  /// `Please check your credentials`
  String get pleaseCheckYourCredentials {
    return Intl.message(
      'Please check your credentials',
      name: 'pleaseCheckYourCredentials',
      desc: '',
      args: [],
    );
  }

  /// `Remaining Maintenance`
  String get remainingMaintenance {
    return Intl.message(
      'Remaining Maintenance',
      name: 'remainingMaintenance',
      desc: '',
      args: [],
    );
  }

  /// `Show on Map`
  String get showOnMap {
    return Intl.message('Show on Map', name: 'showOnMap', desc: '', args: []);
  }

  /// `My Subscriptions`
  String get mySubscriptions {
    return Intl.message(
      'My Subscriptions',
      name: 'mySubscriptions',
      desc: '',
      args: [],
    );
  }

  /// `Days`
  String get days {
    return Intl.message('Days', name: 'days', desc: '', args: []);
  }

  /// `Issuer Name`
  String get issuerName {
    return Intl.message('Issuer Name', name: 'issuerName', desc: '', args: []);
  }

  /// `Issuer Email`
  String get issuerEmail {
    return Intl.message(
      'Issuer Email',
      name: 'issuerEmail',
      desc: '',
      args: [],
    );
  }

  /// `Issuer Phone`
  String get issuerPhone {
    return Intl.message(
      'Issuer Phone',
      name: 'issuerPhone',
      desc: '',
      args: [],
    );
  }

  /// `No data available`
  String get noDataAvailable {
    return Intl.message(
      'No data available',
      name: 'noDataAvailable',
      desc: '',
      args: [],
    );
  }

  /// `Attachment`
  String get attachment {
    return Intl.message('Attachment', name: 'attachment', desc: '', args: []);
  }

  /// `Description`
  String get description {
    return Intl.message('Description', name: 'description', desc: '', args: []);
  }

  /// `Submit`
  String get submit {
    return Intl.message('Submit', name: 'submit', desc: '', args: []);
  }

  /// `Active`
  String get active {
    return Intl.message('Active', name: 'active', desc: '', args: []);
  }

  /// `Archived`
  String get archived {
    return Intl.message('Archived', name: 'archived', desc: '', args: []);
  }

  /// `Recent Active Tickets`
  String get recentActiveTickets {
    return Intl.message(
      'Recent Active Tickets',
      name: 'recentActiveTickets',
      desc: '',
      args: [],
    );
  }

  /// `No replies found`
  String get noRepliesFound {
    return Intl.message(
      'No replies found',
      name: 'noRepliesFound',
      desc: '',
      args: [],
    );
  }

  /// `Replies`
  String get replies {
    return Intl.message('Replies', name: 'replies', desc: '', args: []);
  }

  /// `Total Tickets`
  String get totalTickets {
    return Intl.message(
      'Total Tickets',
      name: 'totalTickets',
      desc: '',
      args: [],
    );
  }

  /// `Start Date`
  String get startDate {
    return Intl.message('Start Date', name: 'startDate', desc: '', args: []);
  }

  /// `End Date`
  String get endDate {
    return Intl.message('End Date', name: 'endDate', desc: '', args: []);
  }

  /// `Are you sure you want to logout?`
  String get areYouSureYouWantToLogout {
    return Intl.message(
      'Are you sure you want to logout?',
      name: 'areYouSureYouWantToLogout',
      desc: '',
      args: [],
    );
  }

  /// `Status`
  String get status {
    return Intl.message('Status', name: 'status', desc: '', args: []);
  }

  /// `Reply`
  String get reply {
    return Intl.message('Reply', name: 'reply', desc: '', args: []);
  }

  /// `Gallery`
  String get gallery {
    return Intl.message('Gallery', name: 'gallery', desc: '', args: []);
  }

  /// `Camera`
  String get camera {
    return Intl.message('Camera', name: 'camera', desc: '', args: []);
  }

  /// `Reply cannot be empty`
  String get replyCannotBeEmpty {
    return Intl.message(
      'Reply cannot be empty',
      name: 'replyCannotBeEmpty',
      desc: '',
      args: [],
    );
  }

  /// `Logout`
  String get logout {
    return Intl.message('Logout', name: 'logout', desc: '', args: []);
  }

  /// `Reply sent successfully`
  String get replySentSuccessfully {
    return Intl.message(
      'Reply sent successfully',
      name: 'replySentSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `English`
  String get english {
    return Intl.message('English', name: 'english', desc: '', args: []);
  }

  /// `العربية`
  String get arabic {
    return Intl.message('العربية', name: 'arabic', desc: '', args: []);
  }

  /// `Cancel`
  String get cancel {
    return Intl.message('Cancel', name: 'cancel', desc: '', args: []);
  }

  /// `Change Language`
  String get changeLanguage {
    return Intl.message(
      'Change Language',
      name: 'changeLanguage',
      desc: '',
      args: [],
    );
  }

  /// `Password`
  String get password {
    return Intl.message('Password', name: 'password', desc: '', args: []);
  }

  /// `Save`
  String get save {
    return Intl.message('Save', name: 'save', desc: '', args: []);
  }

  /// `Welcome back`
  String get welcomeBack {
    return Intl.message(
      'Welcome back',
      name: 'welcomeBack',
      desc: '',
      args: [],
    );
  }

  /// `Welcome\nback`
  String get welcomeBackLine {
    return Intl.message(
      'Welcome\nback',
      name: 'welcomeBackLine',
      desc: '',
      args: [],
    );
  }

  /// `Reports`
  String get reports {
    return Intl.message('Reports', name: 'reports', desc: '', args: []);
  }

  /// `It's great to see you`
  String get itsGreatToSeeYou {
    return Intl.message(
      'It\'s great to see you',
      name: 'itsGreatToSeeYou',
      desc: '',
      args: [],
    );
  }

  /// `All Tickets`
  String get allTickets {
    return Intl.message('All Tickets', name: 'allTickets', desc: '', args: []);
  }

  /// `Username`
  String get username {
    return Intl.message('Username', name: 'username', desc: '', args: []);
  }

  /// `Sort by Date`
  String get sortByDate {
    return Intl.message('Sort by Date', name: 'sortByDate', desc: '', args: []);
  }

  /// `Replied on the ticket`
  String get repliedOnTheTicket {
    return Intl.message(
      'Replied on the ticket',
      name: 'repliedOnTheTicket',
      desc: '',
      args: [],
    );
  }

  /// `New reply on ticket`
  String get newReplyOnTicket {
    return Intl.message(
      'New reply on ticket',
      name: 'newReplyOnTicket',
      desc: '',
      args: [],
    );
  }

  /// `Ascending`
  String get ascending {
    return Intl.message('Ascending', name: 'ascending', desc: '', args: []);
  }

  /// `Confirm`
  String get confirm {
    return Intl.message('Confirm', name: 'confirm', desc: '', args: []);
  }

  /// `Descending`
  String get descending {
    return Intl.message('Descending', name: 'descending', desc: '', args: []);
  }

  /// `Request`
  String get request {
    return Intl.message('Request', name: 'request', desc: '', args: []);
  }

  /// `Issue`
  String get issue {
    return Intl.message('Issue', name: 'issue', desc: '', args: []);
  }

  /// `Add New Ticket`
  String get addNewTicket {
    return Intl.message(
      'Add New Ticket',
      name: 'addNewTicket',
      desc: '',
      args: [],
    );
  }

  /// `Current Time`
  String get currentTime {
    return Intl.message(
      'Current Time',
      name: 'currentTime',
      desc: '',
      args: [],
    );
  }

  /// `Attendance Time`
  String get attendanceTime {
    return Intl.message(
      'Attendance Time',
      name: 'attendanceTime',
      desc: '',
      args: [],
    );
  }

  /// `Location`
  String get location {
    return Intl.message('Location', name: 'location', desc: '', args: []);
  }

  /// `Monthly Statistics`
  String get monthlyStats {
    return Intl.message(
      'Monthly Statistics',
      name: 'monthlyStats',
      desc: '',
      args: [],
    );
  }

  /// `Complete Attends`
  String get completeAttends {
    return Intl.message(
      'Complete Attends',
      name: 'completeAttends',
      desc: '',
      args: [],
    );
  }

  /// `Incomplete Attends`
  String get incompleteAttends {
    return Intl.message(
      'Incomplete Attends',
      name: 'incompleteAttends',
      desc: '',
      args: [],
    );
  }

  /// `Absents`
  String get absents {
    return Intl.message('Absents', name: 'absents', desc: '', args: []);
  }

  /// `Official Holidays`
  String get officialHolidays {
    return Intl.message(
      'Official Holidays',
      name: 'officialHolidays',
      desc: '',
      args: [],
    );
  }

  /// `Request Leaves`
  String get requestLeaves {
    return Intl.message(
      'Request Leaves',
      name: 'requestLeaves',
      desc: '',
      args: [],
    );
  }

  /// `Sick Leaves`
  String get sickLeaves {
    return Intl.message('Sick Leaves', name: 'sickLeaves', desc: '', args: []);
  }

  /// `Active Tasks`
  String get activeTasks {
    return Intl.message(
      'Active Tasks',
      name: 'activeTasks',
      desc: '',
      args: [],
    );
  }

  /// `Finished Tasks`
  String get finishedTasks {
    return Intl.message(
      'Finished Tasks',
      name: 'finishedTasks',
      desc: '',
      args: [],
    );
  }

  /// `Check In`
  String get checkIn {
    return Intl.message('Check In', name: 'checkIn', desc: '', args: []);
  }

  /// `Summary`
  String get summary {
    return Intl.message('Summary', name: 'summary', desc: '', args: []);
  }

  /// `Settings`
  String get settings {
    return Intl.message('Settings', name: 'settings', desc: '', args: []);
  }

  /// `Theme`
  String get theme {
    return Intl.message('Theme', name: 'theme', desc: '', args: []);
  }

  /// `Language`
  String get language {
    return Intl.message('Language', name: 'language', desc: '', args: []);
  }

  /// `System Setting`
  String get systemSetting {
    return Intl.message(
      'System Setting',
      name: 'systemSetting',
      desc: '',
      args: [],
    );
  }

  /// `Light`
  String get light {
    return Intl.message('Light', name: 'light', desc: '', args: []);
  }

  /// `Dark`
  String get dark {
    return Intl.message('Dark', name: 'dark', desc: '', args: []);
  }

  /// `Signature`
  String get signature {
    return Intl.message('Signature', name: 'signature', desc: '', args: []);
  }

  /// `Signatures`
  String get signatures {
    return Intl.message('Signatures', name: 'signatures', desc: '', args: []);
  }

  /// `Add Signature`
  String get addSignature {
    return Intl.message(
      'Add Signature',
      name: 'addSignature',
      desc: '',
      args: [],
    );
  }

  /// `Delivery`
  String get mandob {
    return Intl.message('Delivery', name: 'mandob', desc: '', args: []);
  }

  /// `Admin`
  String get admin {
    return Intl.message('Admin', name: 'admin', desc: '', args: []);
  }

  /// `Office`
  String get office {
    return Intl.message('Office', name: 'office', desc: '', args: []);
  }

  /// `Place`
  String get place {
    return Intl.message('Place', name: 'place', desc: '', args: []);
  }

  /// `Enter Place`
  String get enterPlace {
    return Intl.message('Enter Place', name: 'enterPlace', desc: '', args: []);
  }

  /// `Fingerprint`
  String get fingerprint {
    return Intl.message('Fingerprint', name: 'fingerprint', desc: '', args: []);
  }

  /// `Authenticate`
  String get authenticate {
    return Intl.message(
      'Authenticate',
      name: 'authenticate',
      desc: '',
      args: [],
    );
  }

  /// `Authentication Required`
  String get authenticationRequired {
    return Intl.message(
      'Authentication Required',
      name: 'authenticationRequired',
      desc: '',
      args: [],
    );
  }

  /// `Please authenticate to add signature`
  String get pleaseAuthenticateToAddSignature {
    return Intl.message(
      'Please authenticate to add signature',
      name: 'pleaseAuthenticateToAddSignature',
      desc: '',
      args: [],
    );
  }

  /// `Signature added successfully`
  String get signatureAddedSuccessfully {
    return Intl.message(
      'Signature added successfully',
      name: 'signatureAddedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Failed to add signature`
  String get signatureAddFailed {
    return Intl.message(
      'Failed to add signature',
      name: 'signatureAddFailed',
      desc: '',
      args: [],
    );
  }

  /// `Location Required`
  String get locationRequired {
    return Intl.message(
      'Location Required',
      name: 'locationRequired',
      desc: '',
      args: [],
    );
  }

  /// `Location permission denied`
  String get locationPermissionDenied {
    return Intl.message(
      'Location permission denied',
      name: 'locationPermissionDenied',
      desc: '',
      args: [],
    );
  }

  /// `Biometric authentication not available`
  String get biometricNotAvailable {
    return Intl.message(
      'Biometric authentication not available',
      name: 'biometricNotAvailable',
      desc: '',
      args: [],
    );
  }

  /// `Office Signature`
  String get officeSignature {
    return Intl.message(
      'Office Signature',
      name: 'officeSignature',
      desc: '',
      args: [],
    );
  }

  /// `Regular Signature`
  String get regularSignature {
    return Intl.message(
      'Regular Signature',
      name: 'regularSignature',
      desc: '',
      args: [],
    );
  }

  /// `Today's Signatures`
  String get todaySignatures {
    return Intl.message(
      'Today\'s Signatures',
      name: 'todaySignatures',
      desc: '',
      args: [],
    );
  }

  /// `No signatures today`
  String get noSignaturesToday {
    return Intl.message(
      'No signatures today',
      name: 'noSignaturesToday',
      desc: '',
      args: [],
    );
  }

  /// `Export`
  String get export {
    return Intl.message('Export', name: 'export', desc: '', args: []);
  }

  /// `Export PDF`
  String get exportPdf {
    return Intl.message('Export PDF', name: 'exportPdf', desc: '', args: []);
  }

  /// `Export Excel`
  String get exportExcel {
    return Intl.message(
      'Export Excel',
      name: 'exportExcel',
      desc: '',
      args: [],
    );
  }

  /// `Date Range`
  String get dateRange {
    return Intl.message('Date Range', name: 'dateRange', desc: '', args: []);
  }

  /// `From Date`
  String get fromDate {
    return Intl.message('From Date', name: 'fromDate', desc: '', args: []);
  }

  /// `To Date`
  String get toDate {
    return Intl.message('To Date', name: 'toDate', desc: '', args: []);
  }

  /// `Select Delivery`
  String get selectDelivery {
    return Intl.message(
      'Select Delivery',
      name: 'selectDelivery',
      desc: '',
      args: [],
    );
  }

  /// `Signature Time`
  String get signatureTime {
    return Intl.message(
      'Signature Time',
      name: 'signatureTime',
      desc: '',
      args: [],
    );
  }

  /// `Signature Place`
  String get signaturePlace {
    return Intl.message(
      'Signature Place',
      name: 'signaturePlace',
      desc: '',
      args: [],
    );
  }

  /// `Signature Location`
  String get signatureLocation {
    return Intl.message(
      'Signature Location',
      name: 'signatureLocation',
      desc: '',
      args: [],
    );
  }

  /// `Coordinates`
  String get coordinates {
    return Intl.message('Coordinates', name: 'coordinates', desc: '', args: []);
  }

  /// `First of Day`
  String get firstOfDay {
    return Intl.message('First of Day', name: 'firstOfDay', desc: '', args: []);
  }

  /// `Email`
  String get email {
    return Intl.message('Email', name: 'email', desc: '', args: []);
  }

  /// `Name`
  String get name {
    return Intl.message('Name', name: 'name', desc: '', args: []);
  }

  /// `Create Account`
  String get createAccount {
    return Intl.message(
      'Create Account',
      name: 'createAccount',
      desc: '',
      args: [],
    );
  }

  /// `Already have an account?`
  String get alreadyHaveAccount {
    return Intl.message(
      'Already have an account?',
      name: 'alreadyHaveAccount',
      desc: '',
      args: [],
    );
  }

  /// `Don't have an account?`
  String get dontHaveAccount {
    return Intl.message(
      'Don\'t have an account?',
      name: 'dontHaveAccount',
      desc: '',
      args: [],
    );
  }

  /// `Sign Up`
  String get signUp {
    return Intl.message('Sign Up', name: 'signUp', desc: '', args: []);
  }

  /// `Registration successful`
  String get registrationSuccessful {
    return Intl.message(
      'Registration successful',
      name: 'registrationSuccessful',
      desc: '',
      args: [],
    );
  }

  /// `Registration failed`
  String get registrationFailed {
    return Intl.message(
      'Registration failed',
      name: 'registrationFailed',
      desc: '',
      args: [],
    );
  }

  /// `Login failed`
  String get loginFailed {
    return Intl.message(
      'Login failed',
      name: 'loginFailed',
      desc: '',
      args: [],
    );
  }

  /// `Invalid credentials`
  String get invalidCredentials {
    return Intl.message(
      'Invalid credentials',
      name: 'invalidCredentials',
      desc: '',
      args: [],
    );
  }

  /// `User not found`
  String get userNotFound {
    return Intl.message(
      'User not found',
      name: 'userNotFound',
      desc: '',
      args: [],
    );
  }

  /// `Email already in use`
  String get emailAlreadyInUse {
    return Intl.message(
      'Email already in use',
      name: 'emailAlreadyInUse',
      desc: '',
      args: [],
    );
  }

  /// `Password is too weak`
  String get weakPassword {
    return Intl.message(
      'Password is too weak',
      name: 'weakPassword',
      desc: '',
      args: [],
    );
  }

  /// `Network error`
  String get networkError {
    return Intl.message(
      'Network error',
      name: 'networkError',
      desc: '',
      args: [],
    );
  }

  /// `Something went wrong`
  String get somethingWentWrong {
    return Intl.message(
      'Something went wrong',
      name: 'somethingWentWrong',
      desc: '',
      args: [],
    );
  }

  /// `Select Date`
  String get selectDate {
    return Intl.message('Select Date', name: 'selectDate', desc: '', args: []);
  }

  /// `Office Signatures`
  String get officeSignatures {
    return Intl.message(
      'Office Signatures',
      name: 'officeSignatures',
      desc: '',
      args: [],
    );
  }

  /// `Regular Signatures`
  String get regularSignatures {
    return Intl.message(
      'Regular Signatures',
      name: 'regularSignatures',
      desc: '',
      args: [],
    );
  }

  /// `Total Signatures`
  String get totalSignatures {
    return Intl.message(
      'Total Signatures',
      name: 'totalSignatures',
      desc: '',
      args: [],
    );
  }

  /// `Clients`
  String get clients {
    return Intl.message('Clients', name: 'clients', desc: '', args: []);
  }

  /// `Client`
  String get client {
    return Intl.message('Client', name: 'client', desc: '', args: []);
  }

  /// `Add Client`
  String get addClient {
    return Intl.message('Add Client', name: 'addClient', desc: '', args: []);
  }

  /// `Edit Client`
  String get editClient {
    return Intl.message('Edit Client', name: 'editClient', desc: '', args: []);
  }

  /// `Delete Client`
  String get deleteClient {
    return Intl.message(
      'Delete Client',
      name: 'deleteClient',
      desc: '',
      args: [],
    );
  }

  /// `Client Name`
  String get clientName {
    return Intl.message('Client Name', name: 'clientName', desc: '', args: []);
  }

  /// `Enter client name`
  String get enterClientName {
    return Intl.message(
      'Enter client name',
      name: 'enterClientName',
      desc: '',
      args: [],
    );
  }

  /// `Client Type`
  String get clientType {
    return Intl.message('Client Type', name: 'clientType', desc: '', args: []);
  }

  /// `Client Address`
  String get clientAddress {
    return Intl.message(
      'Client Address',
      name: 'clientAddress',
      desc: '',
      args: [],
    );
  }

  /// `Address`
  String get address {
    return Intl.message('Address', name: 'address', desc: '', args: []);
  }

  /// `Enter address`
  String get enterAddress {
    return Intl.message(
      'Enter address',
      name: 'enterAddress',
      desc: '',
      args: [],
    );
  }

  /// `Lead`
  String get lead {
    return Intl.message('Lead', name: 'lead', desc: '', args: []);
  }

  /// `Deal`
  String get deal {
    return Intl.message('Deal', name: 'deal', desc: '', args: []);
  }

  /// `Client name is required`
  String get clientNameRequired {
    return Intl.message(
      'Client name is required',
      name: 'clientNameRequired',
      desc: '',
      args: [],
    );
  }

  /// `Client address is required`
  String get clientAddressRequired {
    return Intl.message(
      'Client address is required',
      name: 'clientAddressRequired',
      desc: '',
      args: [],
    );
  }

  /// `Location obtained successfully`
  String get locationObtained {
    return Intl.message(
      'Location obtained successfully',
      name: 'locationObtained',
      desc: '',
      args: [],
    );
  }

  /// `Get Location`
  String get getLocation {
    return Intl.message(
      'Get Location',
      name: 'getLocation',
      desc: '',
      args: [],
    );
  }

  /// `Client added successfully`
  String get clientAddedSuccessfully {
    return Intl.message(
      'Client added successfully',
      name: 'clientAddedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Client updated successfully`
  String get clientUpdatedSuccessfully {
    return Intl.message(
      'Client updated successfully',
      name: 'clientUpdatedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Client deleted successfully`
  String get clientDeletedSuccessfully {
    return Intl.message(
      'Client deleted successfully',
      name: 'clientDeletedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Failed to add client`
  String get clientAddFailed {
    return Intl.message(
      'Failed to add client',
      name: 'clientAddFailed',
      desc: '',
      args: [],
    );
  }

  /// `Failed to update client`
  String get clientUpdateFailed {
    return Intl.message(
      'Failed to update client',
      name: 'clientUpdateFailed',
      desc: '',
      args: [],
    );
  }

  /// `Failed to delete client`
  String get clientDeleteFailed {
    return Intl.message(
      'Failed to delete client',
      name: 'clientDeleteFailed',
      desc: '',
      args: [],
    );
  }

  /// `Update Client`
  String get updateClient {
    return Intl.message(
      'Update Client',
      name: 'updateClient',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete {name}?`
  String deleteClientConfirmation(Object name) {
    return Intl.message(
      'Are you sure you want to delete $name?',
      name: 'deleteClientConfirmation',
      desc: '',
      args: [name],
    );
  }

  /// `Delete`
  String get delete {
    return Intl.message('Delete', name: 'delete', desc: '', args: []);
  }

  /// `Edit`
  String get edit {
    return Intl.message('Edit', name: 'edit', desc: '', args: []);
  }

  /// `No clients found`
  String get noClientsFound {
    return Intl.message(
      'No clients found',
      name: 'noClientsFound',
      desc: '',
      args: [],
    );
  }

  /// `No clients match your search`
  String get noClientsMatchSearch {
    return Intl.message(
      'No clients match your search',
      name: 'noClientsMatchSearch',
      desc: '',
      args: [],
    );
  }

  /// `Add First Client`
  String get addFirstClient {
    return Intl.message(
      'Add First Client',
      name: 'addFirstClient',
      desc: '',
      args: [],
    );
  }

  /// `Search clients...`
  String get searchClients {
    return Intl.message(
      'Search clients...',
      name: 'searchClients',
      desc: '',
      args: [],
    );
  }

  /// `Error loading clients`
  String get errorLoadingClients {
    return Intl.message(
      'Error loading clients',
      name: 'errorLoadingClients',
      desc: '',
      args: [],
    );
  }

  /// `Retry`
  String get retry {
    return Intl.message('Retry', name: 'retry', desc: '', args: []);
  }

  /// `Cannot open map`
  String get cannotOpenMap {
    return Intl.message(
      'Cannot open map',
      name: 'cannotOpenMap',
      desc: '',
      args: [],
    );
  }

  /// `Select Client`
  String get selectClient {
    return Intl.message(
      'Select Client',
      name: 'selectClient',
      desc: '',
      args: [],
    );
  }

  /// `Attach to Plan`
  String get attachToPlan {
    return Intl.message(
      'Attach to Plan',
      name: 'attachToPlan',
      desc: '',
      args: [],
    );
  }

  /// `Automatically create or update a plan for this client`
  String get attachToPlanDescription {
    return Intl.message(
      'Automatically create or update a plan for this client',
      name: 'attachToPlanDescription',
      desc: '',
      args: [],
    );
  }

  /// `Plans`
  String get plans {
    return Intl.message('Plans', name: 'plans', desc: '', args: []);
  }

  /// `Plan`
  String get plan {
    return Intl.message('Plan', name: 'plan', desc: '', args: []);
  }

  /// `Add Plan`
  String get addPlan {
    return Intl.message('Add Plan', name: 'addPlan', desc: '', args: []);
  }

  /// `Edit Plan`
  String get editPlan {
    return Intl.message('Edit Plan', name: 'editPlan', desc: '', args: []);
  }

  /// `Delete Plan`
  String get deletePlan {
    return Intl.message('Delete Plan', name: 'deletePlan', desc: '', args: []);
  }

  /// `Plan Date`
  String get planDate {
    return Intl.message('Plan Date', name: 'planDate', desc: '', args: []);
  }

  /// `Start Time`
  String get startTime {
    return Intl.message('Start Time', name: 'startTime', desc: '', args: []);
  }

  /// `End Time`
  String get endTime {
    return Intl.message('End Time', name: 'endTime', desc: '', args: []);
  }

  /// `Note`
  String get note {
    return Intl.message('Note', name: 'note', desc: '', args: []);
  }

  /// `Enter note`
  String get enterNote {
    return Intl.message('Enter note', name: 'enterNote', desc: '', args: []);
  }

  /// `Planned`
  String get planned {
    return Intl.message('Planned', name: 'planned', desc: '', args: []);
  }

  /// `Attended`
  String get attended {
    return Intl.message('Attended', name: 'attended', desc: '', args: []);
  }

  /// `Missed`
  String get missed {
    return Intl.message('Missed', name: 'missed', desc: '', args: []);
  }

  /// `Plan added successfully`
  String get planAddedSuccessfully {
    return Intl.message(
      'Plan added successfully',
      name: 'planAddedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Plan updated successfully`
  String get planUpdatedSuccessfully {
    return Intl.message(
      'Plan updated successfully',
      name: 'planUpdatedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Plan deleted successfully`
  String get planDeletedSuccessfully {
    return Intl.message(
      'Plan deleted successfully',
      name: 'planDeletedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Failed to add plan`
  String get planAddFailed {
    return Intl.message(
      'Failed to add plan',
      name: 'planAddFailed',
      desc: '',
      args: [],
    );
  }

  /// `Failed to update plan`
  String get planUpdateFailed {
    return Intl.message(
      'Failed to update plan',
      name: 'planUpdateFailed',
      desc: '',
      args: [],
    );
  }

  /// `Failed to delete plan`
  String get planDeleteFailed {
    return Intl.message(
      'Failed to delete plan',
      name: 'planDeleteFailed',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this plan?`
  String get deletePlanConfirmation {
    return Intl.message(
      'Are you sure you want to delete this plan?',
      name: 'deletePlanConfirmation',
      desc: '',
      args: [],
    );
  }

  /// `No plans found`
  String get noPlansFound {
    return Intl.message(
      'No plans found',
      name: 'noPlansFound',
      desc: '',
      args: [],
    );
  }

  /// `No plans found for today`
  String get noPlansTodayFound {
    return Intl.message(
      'No plans found for today',
      name: 'noPlansTodayFound',
      desc: '',
      args: [],
    );
  }

  /// `Add First Plan`
  String get addFirstPlan {
    return Intl.message(
      'Add First Plan',
      name: 'addFirstPlan',
      desc: '',
      args: [],
    );
  }

  /// `Timeline`
  String get timeline {
    return Intl.message('Timeline', name: 'timeline', desc: '', args: []);
  }

  /// `Daily Timeline`
  String get dailyTimeline {
    return Intl.message(
      'Daily Timeline',
      name: 'dailyTimeline',
      desc: '',
      args: [],
    );
  }

  /// `Plan Details`
  String get planDetails {
    return Intl.message(
      'Plan Details',
      name: 'planDetails',
      desc: '',
      args: [],
    );
  }

  /// `Attendance Details`
  String get attendanceDetails {
    return Intl.message(
      'Attendance Details',
      name: 'attendanceDetails',
      desc: '',
      args: [],
    );
  }

  /// `Attendances`
  String get attendances {
    return Intl.message('Attendances', name: 'attendances', desc: '', args: []);
  }

  /// `No attendances`
  String get noAttendances {
    return Intl.message(
      'No attendances',
      name: 'noAttendances',
      desc: '',
      args: [],
    );
  }

  /// `Signature Image`
  String get signatureImage {
    return Intl.message(
      'Signature Image',
      name: 'signatureImage',
      desc: '',
      args: [],
    );
  }

  /// `View Signature`
  String get viewSignature {
    return Intl.message(
      'View Signature',
      name: 'viewSignature',
      desc: '',
      args: [],
    );
  }

  /// `Plan Status`
  String get planStatus {
    return Intl.message('Plan Status', name: 'planStatus', desc: '', args: []);
  }

  /// `Client Information`
  String get clientInfo {
    return Intl.message(
      'Client Information',
      name: 'clientInfo',
      desc: '',
      args: [],
    );
  }

  /// `Plan Information`
  String get planInfo {
    return Intl.message(
      'Plan Information',
      name: 'planInfo',
      desc: '',
      args: [],
    );
  }

  /// `Time Range`
  String get timeRange {
    return Intl.message('Time Range', name: 'timeRange', desc: '', args: []);
  }

  /// `Total Plans`
  String get totalPlans {
    return Intl.message('Total Plans', name: 'totalPlans', desc: '', args: []);
  }

  /// `Total Attended`
  String get totalAttended {
    return Intl.message(
      'Total Attended',
      name: 'totalAttended',
      desc: '',
      args: [],
    );
  }

  /// `Total Missed`
  String get totalMissed {
    return Intl.message(
      'Total Missed',
      name: 'totalMissed',
      desc: '',
      args: [],
    );
  }

  /// `Total Planned`
  String get totalPlanned {
    return Intl.message(
      'Total Planned',
      name: 'totalPlanned',
      desc: '',
      args: [],
    );
  }

  /// `Attendance Rate`
  String get attendanceRate {
    return Intl.message(
      'Attendance Rate',
      name: 'attendanceRate',
      desc: '',
      args: [],
    );
  }

  /// `Statistics`
  String get statistics {
    return Intl.message('Statistics', name: 'statistics', desc: '', args: []);
  }

  /// `Close`
  String get close {
    return Intl.message('Close', name: 'close', desc: '', args: []);
  }

  /// `Total Attendances`
  String get totalAttendances {
    return Intl.message(
      'Total Attendances',
      name: 'totalAttendances',
      desc: '',
      args: [],
    );
  }

  /// `End time must be after start time`
  String get endTimeMustBeAfterStartTime {
    return Intl.message(
      'End time must be after start time',
      name: 'endTimeMustBeAfterStartTime',
      desc: '',
      args: [],
    );
  }

  /// `Migration`
  String get migration {
    return Intl.message('Migration', name: 'migration', desc: '', args: []);
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'ar'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
