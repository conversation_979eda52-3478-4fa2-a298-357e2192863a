{"noDataFound": "No data found", "enter": "Enter", "pickImage": "Pick Image", "search": "Search", "welcomeWithName": "Welcome, {name}", "home": "Home", "areYouSureLogout": "Are you sure you want to logout?", "logoutConfirmation": "Logout Confirmation", "register": "Register", "login": "<PERSON><PERSON>", "pleaseCheckYourCredentials": "Please check your credentials", "remainingMaintenance": "Remaining Maintenance", "showOnMap": "Show on Map", "mySubscriptions": "My Subscriptions", "days": "Days", "issuerName": "Issuer Name", "issuerEmail": "Issuer <PERSON><PERSON>", "issuerPhone": "Issuer Phone", "noDataAvailable": "No data available", "attachment": "Attachment", "description": "Description", "submit": "Submit", "active": "Active", "archived": "Archived", "recentActiveTickets": "Recent Active Tickets", "noRepliesFound": "No replies found", "replies": "Replies", "totalTickets": "Total Tickets", "startDate": "Start Date", "endDate": "End Date", "areYouSureYouWantToLogout": "Are you sure you want to logout?", "status": "Status", "reply": "Reply", "gallery": "Gallery", "camera": "Camera", "replyCannotBeEmpty": "Reply cannot be empty", "logout": "Logout", "replySentSuccessfully": "Reply sent successfully", "english": "English", "arabic": "العربية", "cancel": "Cancel", "changeLanguage": "Change Language", "password": "Password", "save": "Save", "welcomeBack": "Welcome back", "welcomeBackLine": "Welcome\nback", "reports": "Reports", "itsGreatToSeeYou": "It's great to see you", "allTickets": "All Tickets", "username": "Username", "sortByDate": "Sort by Date", "repliedOnTheTicket": "Replied on the ticket", "newReplyOnTicket": "New reply on ticket", "ascending": "Ascending", "confirm": "Confirm", "descending": "Descending", "request": "Request", "issue": "Issue", "addNewTicket": "Add New Ticket", "currentTime": "Current Time", "attendanceTime": "Attendance Time", "location": "Location", "monthlyStats": "Monthly Statistics", "completeAttends": "Complete Attends", "incompleteAttends": "Incomplete Attends", "absents": "Absents", "officialHolidays": "Official Holidays", "requestLeaves": "Request Leaves", "sickLeaves": "Sick Leaves", "activeTasks": "Active Tasks", "finishedTasks": "Finished Tasks", "checkIn": "Check In", "summary": "Summary", "settings": "Settings", "theme": "Theme", "language": "Language", "systemSetting": "System Setting", "light": "Light", "dark": "Dark", "signature": "Signature", "signatures": "Signatures", "addSignature": "Add Signature", "mandob": "Delivery", "admin": "Admin", "office": "Office", "place": "Place", "enterPlace": "Enter Place", "fingerprint": "Fingerprint", "authenticate": "Authenticate", "authenticationRequired": "Authentication Required", "pleaseAuthenticateToAddSignature": "Please authenticate to add signature", "signatureAddedSuccessfully": "Signature added successfully", "signatureAddFailed": "Failed to add signature", "locationRequired": "Location Required", "locationPermissionDenied": "Location permission denied", "biometricNotAvailable": "Biometric authentication not available", "officeSignature": "Office Signature", "regularSignature": "Regular Signature", "todaySignatures": "Today's Signatures", "noSignaturesToday": "No signatures today", "export": "Export", "exportPdf": "Export PDF", "exportExcel": "Export Excel", "dateRange": "Date Range", "fromDate": "From Date", "toDate": "To Date", "selectDelivery": "Select Delivery", "signatureTime": "Signature Time", "signaturePlace": "Signature Place", "signatureLocation": "Signature Location", "coordinates": "Coordinates", "firstOfDay": "First of Day", "email": "Email", "name": "Name", "createAccount": "Create Account", "alreadyHaveAccount": "Already have an account?", "dontHaveAccount": "Don't have an account?", "signUp": "Sign Up", "registrationSuccessful": "Registration successful", "registrationFailed": "Registration failed", "loginFailed": "<PERSON><PERSON> failed", "invalidCredentials": "Invalid credentials", "userNotFound": "User not found", "emailAlreadyInUse": "Email already in use", "weakPassword": "Password is too weak", "networkError": "Network error", "somethingWentWrong": "Something went wrong", "selectDate": "Select Date", "officeSignatures": "Office Signatures", "regularSignatures": "Regular Signatures", "totalSignatures": "Total Signatures", "clients": "Clients", "client": "Client", "addClient": "Add Client", "editClient": "Edit Client", "deleteClient": "Delete Client", "clientName": "Client Name", "enterClientName": "Enter client name", "clientType": "Client Type", "clientAddress": "Client Address", "address": "Address", "enterAddress": "Enter address", "lead": "Lead", "deal": "Deal", "clientNameRequired": "Client name is required", "clientAddressRequired": "Client address is required", "locationObtained": "Location obtained successfully", "getLocation": "Get Location", "clientAddedSuccessfully": "Client added successfully", "clientUpdatedSuccessfully": "Client updated successfully", "clientDeletedSuccessfully": "Client deleted successfully", "clientAddFailed": "Failed to add client", "clientUpdateFailed": "Failed to update client", "clientDeleteFailed": "Failed to delete client", "updateClient": "Update Client", "deleteClientConfirmation": "Are you sure you want to delete {name}?", "delete": "Delete", "edit": "Edit", "noClientsFound": "No clients found", "noClientsMatchSearch": "No clients match your search", "addFirstClient": "Add First Client", "searchClients": "Search clients...", "errorLoadingClients": "Error loading clients", "retry": "Retry", "cannotOpenMap": "Cannot open map", "selectClient": "Select Client", "attachToPlan": "Attach to Plan", "attachToPlanDescription": "Automatically create or update a plan for this client", "plans": "Plans", "plan": "Plan", "addPlan": "Add Plan", "editPlan": "Edit Plan", "deletePlan": "Delete Plan", "planDate": "Plan Date", "startTime": "Start Time", "endTime": "End Time", "note": "Note", "enterNote": "Enter note", "planned": "Planned", "attended": "Attended", "missed": "Missed", "planAddedSuccessfully": "Plan added successfully", "planUpdatedSuccessfully": "Plan updated successfully", "planDeletedSuccessfully": "Plan deleted successfully", "planAddFailed": "Failed to add plan", "planUpdateFailed": "Failed to update plan", "planDeleteFailed": "Failed to delete plan", "deletePlanConfirmation": "Are you sure you want to delete this plan?", "noPlansFound": "No plans found", "noPlansTodayFound": "No plans found for today", "addFirstPlan": "Add First Plan", "timeline": "Timeline", "dailyTimeline": "Daily Timeline", "planDetails": "Plan Details", "attendanceDetails": "Attendance Details", "attendances": "Attendances", "noAttendances": "No attendances", "signatureImage": "Signature Image", "viewSignature": "View Signature", "planStatus": "Plan Status", "clientInfo": "Client Information", "planInfo": "Plan Information", "timeRange": "Time Range", "totalPlans": "Total Plans", "totalAttended": "Total Attended", "totalMissed": "Total Missed", "totalPlanned": "Total Planned", "attendanceRate": "Attendance Rate", "statistics": "Statistics", "close": "Close", "totalAttendances": "Total Attendances", "endTimeMustBeAfterStartTime": "End time must be after start time"}