{"snapshots": {"/Users/<USER>/Flutter-Projects/Optimum-Apps/marketing_team/lib/src/core/consts/app_constants.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Optimum-Apps/marketing_team/lib/src/core/consts/app_constants.dart", "baseContent": "import 'package:flutter/material.dart' show Locale, LocalizationsDelegate;\nimport 'package:flutter_localizations/flutter_localizations.dart';\nimport 'package:form_builder_validators/form_builder_validators.dart';\nimport 'package:opti_tickets/generated/l10n.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nclass AppConsts {\n  static const String appName = '';\n  static const Locale locale = Locale('en');\n\n  static const List<Locale> supportedLocales = [\n    locale,\n    Locale('ar'),\n  ];\n\n  static bool get isEnglish =>\n      GetStorageService.getData(key: LocalKeys.language) == 'en';\n\n  static const List<LocalizationsDelegate> localizationsDelegates = [\n    S.delegate,\n    FormBuilderLocalizations.delegate,\n    GlobalMaterialLocalizations.delegate,\n    GlobalCupertinoLocalizations.delegate,\n    GlobalWidgetsLocalizations.delegate,\n  ];\n\n  //? Test Login\n  // static const String testEmail = 'HASAB_FSLSC';\n  static const String testEmail = '<EMAIL>';\n  static const String testPass = 'mbahi@123';\n}\n", "baseTimestamp": 1757924966609, "deltas": [{"timestamp": 1757924977899, "changes": [{"type": "MODIFY", "lineNumber": 7, "content": "  static const String appName = 'Marketing Team';", "oldContent": "  static const String appName = '';"}]}, {"timestamp": 1758116314653, "changes": [{"type": "INSERT", "lineNumber": 29, "content": "  static const String testEmail = '<EMAIL>';"}]}, {"timestamp": 1758116317562, "changes": [{"type": "MODIFY", "lineNumber": 29, "content": "  // static const String testEmail = '<EMAIL>';", "oldContent": "  static const String testEmail = '<EMAIL>';"}]}, {"timestamp": 1758116322204, "changes": [{"type": "INSERT", "lineNumber": 31, "content": "  // static const String testPass = 'mbahi@123';"}]}, {"timestamp": 1758116327032, "changes": [{"type": "MODIFY", "lineNumber": 30, "content": "  static const String testPass = '12345678';", "oldContent": "  static const String testPass = 'mbahi@123';"}]}, {"timestamp": 1758116331160, "changes": [{"type": "DELETE", "lineNumber": 28, "oldContent": "  static const String testEmail = '<EMAIL>';"}, {"type": "MODIFY", "lineNumber": 28, "content": "  // static const String testEmail = '<EMAIL>';", "oldContent": "  // static const String testEmail = '<EMAIL>';"}, {"type": "INSERT", "lineNumber": 29, "content": "  static const String testEmail = '<EMAIL>';"}]}]}, "/Users/<USER>/Flutter-Projects/Optimum-Apps/marketing_team/packages/xr_helper/pubspec.yaml": {"filePath": "/Users/<USER>/Flutter-Projects/Optimum-Apps/marketing_team/packages/xr_helper/pubspec.yaml", "baseContent": "name: xr_helper\ndescription: \"A new Flutter package project.\"\nversion: 0.0.1\nhomepage:\n\nenvironment:\n  sdk: '>=3.2.0 <4.0.0'\n  flutter: \">=1.17.0\"\n\ndependencies:\n  flutter:\n    sdk: flutter\n\n  cupertino_icons: ^1.0.6\n\n  #? URL Launcher (Make calls, send SMS, open URLs)\n  url_launcher: ^6.2.1\n\n  #? Local Storage\n  get_storage: ^2.1.1\n\n  #? Remote (HTTP Requests)\n  http: ^1.1.2\n\n  #? Sized Box (Height & Width)\n  gap: ^3.0.1\n\n  #? Intl (Date Formatting)\n  intl: ^0.20.2\n\n  #? Theme (Fonts)\n  google_fonts: ^6.1.0\n\n  #? Alerts\n  another_flushbar: ^1.12.30\n  fluttertoast: ^8.2.8\n\n  #? Firebase\n  firebase_core: ^3.6.0\n  firebase_messaging:\n  googleapis:\n  googleapis_auth:\n\n  #? Hooks\n  flutter_hooks: ^0.20.3\n\n  #? UI\n  cached_network_image:\n  shimmer: ^3.0.0\n  flutter_form_builder:\n  form_builder_validators: ^11.0.0\n\n  #? Logger (Log messages with colors)\n  logger: ^2.0.2+1\n\n  #? State Management\n  provider: ^6.1.1\n\n\n\ndev_dependencies:\n  flutter_test:\n    sdk: flutter\n  flutter_lints: ^3.0.1\n\nflutter:\n", "baseTimestamp": 1757925020081, "deltas": [{"timestamp": 1757925022137, "changes": [{"type": "MODIFY", "lineNumber": 50, "content": "  form_builder_validators:", "oldContent": "  form_builder_validators: ^11.0.0"}]}]}, "/Users/<USER>/Flutter-Projects/Optimum-Apps/marketing_team/packages/xr_helper/pubspec.lock": {"filePath": "/Users/<USER>/Flutter-Projects/Optimum-Apps/marketing_team/packages/xr_helper/pubspec.lock", "baseContent": "# Generated by pub\n# See https://dart.dev/tools/pub/glossary#lockfile\npackages:\n  _discoveryapis_commons:\n    dependency: transitive\n    description:\n      name: _discoveryapis_commons\n      sha256: \"113c4100b90a5b70a983541782431b82168b3cae166ab130649c36eb3559d498\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"1.0.7\"\n  _flutterfire_internals:\n    dependency: transitive\n    description:\n      name: _flutterfire_internals\n      sha256: \"5534e701a2c505fed1f0799e652dd6ae23bd4d2c4cf797220e5ced5764a7c1c2\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"1.3.44\"\n  another_flushbar:\n    dependency: \"direct main\"\n    description:\n      name: another_flushbar\n      sha256: \"19bf9520230ec40b300aaf9dd2a8fefcb277b25ecd1c4838f530566965befc2a\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"1.12.30\"\n  args:\n    dependency: transitive\n    description:\n      name: args\n      sha256: bf9f5caeea8d8fe6721a9c358dd8a5c1947b27f1cfaa18b39c301273594919e6\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"2.6.0\"\n  async:\n    dependency: transitive\n    description:\n      name: async\n      sha256: \"947bfcf187f74dbc5e146c9eb9c0f10c9f8b30743e341481c1e2ed3ecc18c20c\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"2.11.0\"\n  boolean_selector:\n    dependency: transitive\n    description:\n      name: boolean_selector\n      sha256: \"6cfb5af12253eaf2b368f07bacc5a80d1301a071c73360d746b7f2e32d762c66\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"2.1.1\"\n  cached_network_image:\n    dependency: \"direct main\"\n    description:\n      name: cached_network_image\n      sha256: f98972704692ba679db144261172a8e20feb145636c617af0eb4022132a6797f\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"3.3.0\"\n  cached_network_image_platform_interface:\n    dependency: transitive\n    description:\n      name: cached_network_image_platform_interface\n      sha256: \"56aa42a7a01e3c9db8456d9f3f999931f1e05535b5a424271e9a38cabf066613\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"3.0.0\"\n  cached_network_image_web:\n    dependency: transitive\n    description:\n      name: cached_network_image_web\n      sha256: \"759b9a9f8f6ccbb66c185df805fac107f05730b1dab9c64626d1008cca532257\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"1.1.0\"\n  characters:\n    dependency: transitive\n    description:\n      name: characters\n      sha256: f71061c654a3380576a52b451dd5532377954cf9dbd272a78fc8479606670803\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"1.4.0\"\n  clock:\n    dependency: transitive\n    description:\n      name: clock\n      sha256: fddb70d9b5277016c77a80201021d40a2247104d9f4aa7bab7157b7e3f05b84b\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"1.1.2\"\n  collection:\n    dependency: transitive\n    description:\n      name: collection\n      sha256: \"2f5709ae4d3d59dd8f7cd309b4e023046b57d8a6c82130785d2b0e5868084e76\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"1.19.1\"\n  crypto:\n    dependency: transitive\n    description:\n      name: crypto\n      sha256: ff625774173754681d66daaf4a448684fb04b78f902da9cb3d308c19cc5e8bab\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"3.0.3\"\n  cupertino_icons:\n    dependency: \"direct main\"\n    description:\n      name: cupertino_icons\n      sha256: d57953e10f9f8327ce64a508a355f0b1ec902193f66288e8cb5070e7c47eeb2d\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"1.0.6\"\n  fake_async:\n    dependency: transitive\n    description:\n      name: fake_async\n      sha256: \"5368f224a74523e8d2e7399ea1638b37aecfca824a3cc4dfdf77bf1fa905ac44\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"1.3.3\"\n  ffi:\n    dependency: transitive\n    description:\n      name: ffi\n      sha256: \"7bf0adc28a23d395f19f3f1eb21dd7cfd1dd9f8e1c50051c069122e6853bc878\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"2.1.0\"\n  file:\n    dependency: transitive\n    description:\n      name: file\n      sha256: \"5fc22d7c25582e38ad9a8515372cd9a93834027aacf1801cf01164dac0ffa08c\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"7.0.0\"\n  firebase_core:\n    dependency: \"direct main\"\n    description:\n      name: firebase_core\n      sha256: \"51dfe2fbf3a984787a2e7b8592f2f05c986bfedd6fdacea3f9e0a7beb334de96\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"3.6.0\"\n  firebase_core_platform_interface:\n    dependency: transitive\n    description:\n      name: firebase_core_platform_interface\n      sha256: e30da58198a6d4b49d5bce4e852f985c32cb10db329ebef9473db2b9f09ce810\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"5.3.0\"\n  firebase_core_web:\n    dependency: transitive\n    description:\n      name: firebase_core_web\n      sha256: f967a7138f5d2ffb1ce15950e2a382924239eaa521150a8f144af34e68b3b3e5\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"2.18.1\"\n  firebase_messaging:\n    dependency: \"direct main\"\n    description:\n      name: firebase_messaging\n      sha256: eb6e28a3a35deda61fe8634967c84215efc19133ba58d8e0fc6c9a2af2cba05e\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"15.1.3\"\n  firebase_messaging_platform_interface:\n    dependency: transitive\n    description:\n      name: firebase_messaging_platform_interface\n      sha256: b316c4ee10d93d32c033644207afc282d9b2b4372f3cf9c6022f3558b3873d2d\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"4.5.46\"\n  firebase_messaging_web:\n    dependency: transitive\n    description:\n      name: firebase_messaging_web\n      sha256: d7f0147a1a9fe4313168e20154a01fd5cf332898de1527d3930ff77b8c7f5387\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"3.9.2\"\n  flutter:\n    dependency: \"direct main\"\n    description: flutter\n    source: sdk\n    version: \"0.0.0\"\n  flutter_cache_manager:\n    dependency: transitive\n    description:\n      name: flutter_cache_manager\n      sha256: \"8207f27539deb83732fdda03e259349046a39a4c767269285f449ade355d54ba\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"3.3.1\"\n  flutter_form_builder:\n    dependency: \"direct main\"\n    description:\n      name: flutter_form_builder\n      sha256: ec74389c4af2361a5e9fe9a36fcfe722698be3f681d713cb3ebe099ae15ed863\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"10.2.0\"\n  flutter_hooks:\n    dependency: \"direct main\"\n    description:\n      name: flutter_hooks\n      sha256: \"7c8db779c2d1010aa7f9ea3fbefe8f86524fcb87b69e8b0af31e1a4b55422dec\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"0.20.3\"\n  flutter_lints:\n    dependency: \"direct dev\"\n    description:\n      name: flutter_lints\n      sha256: e2a421b7e59244faef694ba7b30562e489c2b489866e505074eb005cd7060db7\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"3.0.1\"\n  flutter_localizations:\n    dependency: transitive\n    description: flutter\n    source: sdk\n    version: \"0.0.0\"\n  flutter_test:\n    dependency: \"direct dev\"\n    description: flutter\n    source: sdk\n    version: \"0.0.0\"\n  flutter_web_plugins:\n    dependency: transitive\n    description: flutter\n    source: sdk\n    version: \"0.0.0\"\n  fluttertoast:\n    dependency: \"direct main\"\n    description:\n      name: fluttertoast\n      sha256: \"95f349437aeebe524ef7d6c9bde3e6b4772717cf46a0eb6a3ceaddc740b297cc\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"8.2.8\"\n  form_builder_validators:\n    dependency: \"direct main\"\n    description:\n      name: form_builder_validators\n      sha256: \"1b03c74d1db740890e6af803b43e5ebe56f8fa1ff5609cbf744e8d980dc5f8c6\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"11.2.0\"\n  gap:\n    dependency: \"direct main\"\n    description:\n      name: gap\n      sha256: f19387d4e32f849394758b91377f9153a1b41d79513ef7668c088c77dbc6955d\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"3.0.1\"\n  get:\n    dependency: transitive\n    description:\n      name: get\n      sha256: e4e7335ede17452b391ed3b2ede016545706c01a02292a6c97619705e7d2a85e\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"4.6.6\"\n  get_storage:\n    dependency: \"direct main\"\n    description:\n      name: get_storage\n      sha256: \"39db1fffe779d0c22b3a744376e86febe4ade43bf65e06eab5af707dc84185a2\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"2.1.1\"\n  google_fonts:\n    dependency: \"direct main\"\n    description:\n      name: google_fonts\n      sha256: f0b8d115a13ecf827013ec9fc883390ccc0e87a96ed5347a3114cac177ef18e8\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"6.1.0\"\n  google_identity_services_web:\n    dependency: transitive\n    description:\n      name: google_identity_services_web\n      sha256: \"55580f436822d64c8ff9a77e37d61f5fb1e6c7ec9d632a43ee324e2a05c3c6c9\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"0.3.3\"\n  googleapis:\n    dependency: \"direct main\"\n    description:\n      name: googleapis\n      sha256: \"864f222aed3f2ff00b816c675edf00a39e2aaf373d728d8abec30b37bee1a81c\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"13.2.0\"\n  googleapis_auth:\n    dependency: \"direct main\"\n    description:\n      name: googleapis_auth\n      sha256: befd71383a955535060acde8792e7efc11d2fccd03dd1d3ec434e85b68775938\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"1.6.0\"\n  http:\n    dependency: \"direct main\"\n    description:\n      name: http\n      sha256: b9c29a161230ee03d3ccf545097fccd9b87a5264228c5d348202e0f0c28f9010\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"1.2.2\"\n  http_parser:\n    dependency: transitive\n    description:\n      name: http_parser\n      sha256: \"2aa08ce0341cc9b354a498388e30986515406668dbcc4f7c950c3e715496693b\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"4.0.2\"\n  intl:\n    dependency: \"direct main\"\n    description:\n      name: intl\n      sha256: \"3df61194eb431efc39c4ceba583b95633a403f46c9fd341e550ce0bfa50e9aa5\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"0.20.2\"\n  leak_tracker:\n    dependency: transitive\n    description:\n      name: leak_tracker\n      sha256: \"33e2e26bdd85a0112ec15400c8cbffea70d0f9c3407491f672a2fad47915e2de\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"11.0.2\"\n  leak_tracker_flutter_testing:\n    dependency: transitive\n    description:\n      name: leak_tracker_flutter_testing\n      sha256: \"1dbc140bb5a23c75ea9c4811222756104fbcd1a27173f0c34ca01e16bea473c1\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"3.0.10\"\n  leak_tracker_testing:\n    dependency: transitive\n    description:\n      name: leak_tracker_testing\n      sha256: \"8d5a2d49f4a66b49744b23b018848400d23e54caf9463f4eb20df3eb8acb2eb1\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"3.0.2\"\n  lints:\n    dependency: transitive\n    description:\n      name: lints\n      sha256: cbf8d4b858bb0134ef3ef87841abdf8d63bfc255c266b7bf6b39daa1085c4290\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"3.0.0\"\n  logger:\n    dependency: \"direct main\"\n    description:\n      name: logger\n      sha256: \"6bbb9d6f7056729537a4309bda2e74e18e5d9f14302489cc1e93f33b3fe32cac\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"2.0.2+1\"\n  matcher:\n    dependency: transitive\n    description:\n      name: matcher\n      sha256: dc58c723c3c24bf8d3e2d3ad3f2f9d7bd9cf43ec6feaa64181775e60190153f2\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"0.12.17\"\n  material_color_utilities:\n    dependency: transitive\n    description:\n      name: material_color_utilities\n      sha256: f7142bb1154231d7ea5f96bc7bde4bda2a0945d2806bb11670e30b850d56bdec\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"0.11.1\"\n  meta:\n    dependency: transitive\n    description:\n      name: meta\n      sha256: e3641ec5d63ebf0d9b41bd43201a66e3fc79a65db5f61fc181f04cd27aab950c\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"1.16.0\"\n  nested:\n    dependency: transitive\n    description:\n      name: nested\n      sha256: \"03bac4c528c64c95c722ec99280375a6f2fc708eec17c7b3f07253b626cd2a20\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"1.0.0\"\n  octo_image:\n    dependency: transitive\n    description:\n      name: octo_image\n      sha256: \"45b40f99622f11901238e18d48f5f12ea36426d8eced9f4cbf58479c7aa2430d\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"2.0.0\"\n  path:\n    dependency: transitive\n    description:\n      name: path\n      sha256: \"75cca69d1490965be98c73ceaea117e8a04dd21217b37b292c9ddbec0d955bc5\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"1.9.1\"\n  path_provider:\n    dependency: transitive\n    description:\n      name: path_provider\n      sha256: a1aa8aaa2542a6bc57e381f132af822420216c80d4781f7aa085ca3229208aaa\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"2.1.1\"\n  path_provider_android:\n    dependency: transitive\n    description:\n      name: path_provider_android\n      sha256: e595b98692943b4881b219f0a9e3945118d3c16bd7e2813f98ec6e532d905f72\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"2.2.1\"\n  path_provider_foundation:\n    dependency: transitive\n    description:\n      name: path_provider_foundation\n      sha256: \"19314d595120f82aca0ba62787d58dde2cc6b5df7d2f0daf72489e38d1b57f2d\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"2.3.1\"\n  path_provider_linux:\n    dependency: transitive\n    description:\n      name: path_provider_linux\n      sha256: f7a1fe3a634fe7734c8d3f2766ad746ae2a2884abe22e241a8b301bf5cac3279\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"2.2.1\"\n  path_provider_platform_interface:\n    dependency: transitive\n    description:\n      name: path_provider_platform_interface\n      sha256: \"94b1e0dd80970c1ce43d5d4e050a9918fce4f4a775e6142424c30a29a363265c\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"2.1.1\"\n  path_provider_windows:\n    dependency: transitive\n    description:\n      name: path_provider_windows\n      sha256: \"8bc9f22eee8690981c22aa7fc602f5c85b497a6fb2ceb35ee5a5e5ed85ad8170\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"2.2.1\"\n  platform:\n    dependency: transitive\n    description:\n      name: platform\n      sha256: \"0a279f0707af40c890e80b1e9df8bb761694c074ba7e1d4ab1bc4b728e200b59\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"3.1.3\"\n  plugin_platform_interface:\n    dependency: transitive\n    description:\n      name: plugin_platform_interface\n      sha256: f4f88d4a900933e7267e2b353594774fc0d07fb072b47eedcd5b54e1ea3269f8\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"2.1.7\"\n  provider:\n    dependency: \"direct main\"\n    description:\n      name: provider\n      sha256: \"9a96a0a19b594dbc5bf0f1f27d2bc67d5f95957359b461cd9feb44ed6ae75096\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"6.1.1\"\n  rxdart:\n    dependency: transitive\n    description:\n      name: rxdart\n      sha256: \"0c7c0cedd93788d996e33041ffecda924cc54389199cde4e6a34b440f50044cb\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"0.27.7\"\n  shimmer:\n    dependency: \"direct main\"\n    description:\n      name: shimmer\n      sha256: \"5f88c883a22e9f9f299e5ba0e4f7e6054857224976a5d9f839d4ebdc94a14ac9\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"3.0.0\"\n  sky_engine:\n    dependency: transitive\n    description: flutter\n    source: sdk\n    version: \"0.0.0\"\n  source_span:\n    dependency: transitive\n    description:\n      name: source_span\n      sha256: \"53e943d4206a5e30df338fd4c6e7a077e02254531b138a15aec3bd143c1a8b3c\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"1.10.0\"\n  sqflite:\n    dependency: transitive\n    description:\n      name: sqflite\n      sha256: \"591f1602816e9c31377d5f008c2d9ef7b8aca8941c3f89cc5fd9d84da0c38a9a\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"2.3.0\"\n  sqflite_common:\n    dependency: transitive\n    description:\n      name: sqflite_common\n      sha256: bb4738f15b23352822f4c42a531677e5c6f522e079461fd240ead29d8d8a54a6\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"2.5.0+2\"\n  stack_trace:\n    dependency: transitive\n    description:\n      name: stack_trace\n      sha256: \"8b27215b45d22309b5cddda1aa2b19bdfec9df0e765f2de506401c071d38d1b1\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"1.12.1\"\n  stream_channel:\n    dependency: transitive\n    description:\n      name: stream_channel\n      sha256: \"969e04c80b8bcdf826f8f16579c7b14d780458bd97f56d107d3950fdbeef059d\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"2.1.4\"\n  string_scanner:\n    dependency: transitive\n    description:\n      name: string_scanner\n      sha256: \"556692adab6cfa87322a115640c11f13cb77b3f076ddcc5d6ae3c20242bedcde\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"1.2.0\"\n  synchronized:\n    dependency: transitive\n    description:\n      name: synchronized\n      sha256: \"5fcbd27688af6082f5abd611af56ee575342c30e87541d0245f7ff99faa02c60\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"3.1.0\"\n  term_glyph:\n    dependency: transitive\n    description:\n      name: term_glyph\n      sha256: a29248a84fbb7c79282b40b8c72a1209db169a2e0542bce341da992fe1bc7e84\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"1.2.1\"\n  test_api:\n    dependency: transitive\n    description:\n      name: test_api\n      sha256: \"522f00f556e73044315fa4585ec3270f1808a4b186c936e612cab0b565ff1e00\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"0.7.6\"\n  typed_data:\n    dependency: transitive\n    description:\n      name: typed_data\n      sha256: facc8d6582f16042dd49f2463ff1bd6e2c9ef9f3d5da3d9b087e244a7b564b3c\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"1.3.2\"\n  url_launcher:\n    dependency: \"direct main\"\n    description:\n      name: url_launcher\n      sha256: b1c9e98774adf8820c96fbc7ae3601231d324a7d5ebd8babe27b6dfac91357ba\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"6.2.1\"\n  url_launcher_android:\n    dependency: transitive\n    description:\n      name: url_launcher_android\n      sha256: \"31222ffb0063171b526d3e569079cf1f8b294075ba323443fdc690842bfd4def\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"6.2.0\"\n  url_launcher_ios:\n    dependency: transitive\n    description:\n      name: url_launcher_ios\n      sha256: bba3373219b7abb6b5e0d071b0fe66dfbe005d07517a68e38d4fc3638f35c6d3\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"6.2.1\"\n  url_launcher_linux:\n    dependency: transitive\n    description:\n      name: url_launcher_linux\n      sha256: \"9f2d390e096fdbe1e6e6256f97851e51afc2d9c423d3432f1d6a02a8a9a8b9fd\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"3.1.0\"\n  url_launcher_macos:\n    dependency: transitive\n    description:\n      name: url_launcher_macos\n      sha256: b7244901ea3cf489c5335bdacda07264a6e960b1c1b1a9f91e4bc371d9e68234\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"3.1.0\"\n  url_launcher_platform_interface:\n    dependency: transitive\n    description:\n      name: url_launcher_platform_interface\n      sha256: \"980e8d9af422f477be6948bdfb68df8433be71f5743a188968b0c1b887807e50\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"2.2.0\"\n  url_launcher_web:\n    dependency: transitive\n    description:\n      name: url_launcher_web\n      sha256: \"772638d3b34c779ede05ba3d38af34657a05ac55b06279ea6edd409e323dca8e\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"2.3.3\"\n  url_launcher_windows:\n    dependency: transitive\n    description:\n      name: url_launcher_windows\n      sha256: \"7754a1ad30ee896b265f8d14078b0513a4dba28d358eabb9d5f339886f4a1adc\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"3.1.0\"\n  uuid:\n    dependency: transitive\n    description:\n      name: uuid\n      sha256: \"648e103079f7c64a36dc7d39369cabb358d377078a051d6ae2ad3aa539519313\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"3.0.7\"\n  vector_math:\n    dependency: transitive\n    description:\n      name: vector_math\n      sha256: d530bd74fea330e6e364cda7a85019c434070188383e1cd8d9777ee586914c5b\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"2.2.0\"\n  vm_service:\n    dependency: transitive\n    description:\n      name: vm_service\n      sha256: \"5c5f338a667b4c644744b661f309fb8080bb94b18a7e91ef1dbd343bed00ed6d\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"14.2.5\"\n  web:\n    dependency: transitive\n    description:\n      name: web\n      sha256: cd3543bd5798f6ad290ea73d210f423502e71900302dde696f8bff84bf89a1cb\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"1.1.0\"\n  win32:\n    dependency: transitive\n    description:\n      name: win32\n      sha256: \"7c99c0e1e2fa190b48d25c81ca5e42036d5cac81430ef249027d97b0935c553f\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"5.1.0\"\n  xdg_directories:\n    dependency: transitive\n    description:\n      name: xdg_directories\n      sha256: \"589ada45ba9e39405c198fe34eb0f607cddb2108527e658136120892beac46d2\"\n      url: \"https://pub.dev\"\n    source: hosted\n    version: \"1.0.3\"\nsdks:\n  dart: \">=3.9.0 <4.0.0\"\n  flutter: \">=3.35.0\"\n", "baseTimestamp": 1757925026554}, "/Users/<USER>/Flutter-Projects/Optimum-Apps/marketing_team/android/settings.gradle": {"filePath": "/Users/<USER>/Flutter-Projects/Optimum-Apps/marketing_team/android/settings.gradle", "baseContent": "pluginManagement {\n    def flutterSdkPath = {\n        def properties = new Properties()\n        file(\"local.properties\").withInputStream { properties.load(it) }\n        def flutterSdkPath = properties.getProperty(\"flutter.sdk\")\n        assert flutterSdkPath != null, \"flutter.sdk not set in local.properties\"\n        return flutterSdkPath\n    }()\n\n    includeBuild(\"$flutterSdkPath/packages/flutter_tools/gradle\")\n\n    repositories {\n        google()\n        mavenCentral()\n        gradlePluginPortal()\n    }\n}\n\nplugins {\n    id \"dev.flutter.flutter-plugin-loader\" version \"1.0.0\"\n    id \"com.android.application\" version \"8.6.0\" apply false\n    // START: FlutterFire Configuration\n    id \"com.google.gms.google-services\" version \"4.3.15\" apply false\n    // END: FlutterFire Configuration\n    id \"org.jetbrains.kotlin.android\" version \"1.8.22\" apply false\n}\n\ninclude \":app\"\n", "baseTimestamp": 1758114009951, "deltas": [{"timestamp": 1758114016527, "changes": [{"type": "MODIFY", "lineNumber": 24, "content": "    id \"org.jetbrains.kotlin.android\" version \"2.1.0\" apply false", "oldContent": "    id \"org.jetbrains.kotlin.android\" version \"1.8.22\" apply false"}]}]}, "/Users/<USER>/Flutter-Projects/Optimum-Apps/marketing_team/android/app/build.gradle": {"filePath": "/Users/<USER>/Flutter-Projects/Optimum-Apps/marketing_team/android/app/build.gradle", "baseContent": "plugins {\n    id \"com.android.application\"\n    // START: FlutterFire Configuration\n    id 'com.google.gms.google-services'\n    // END: FlutterFire Configuration\n    id \"kotlin-android\"\n    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.\n    id \"dev.flutter.flutter-gradle-plugin\"\n}\n\nandroid {\n    namespace = \"com.opti4it.opti_tickets\"\n    compileSdkVersion = 36\n    ndkVersion = \"28.0.13004108\"\n\n    compileOptions {\n        sourceCompatibility = JavaVersion.VERSION_1_8\n        targetCompatibility = JavaVersion.VERSION_1_8\n    }\n\n    kotlinOptions {\n        jvmTarget = JavaVersion.VERSION_1_8\n    }\n\n    defaultConfig {\n        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).\n        applicationId = \"com.opti4it.mandob_tracking\"\n        // You can update the following values to match your application needs.\n        // For more information, see: https://flutter.dev/to/review-gradle-config.\n        minSdkVersion flutter.minSdkVersion\n        targetSdkVersion 35\n        versionCode = flutter.versionCode\n        versionName = flutter.versionName\n    }\n\n    buildTypes {\n        release {\n            // TODO: Add your own signing config for the release build.\n            // Signing with the debug keys for now, so `flutter run --release` works.\n            signingConfig = signingConfigs.debug\n        }\n    }\n}\n\nflutter {\n    source = \"../..\"\n}\n", "baseTimestamp": 1758114022930, "deltas": [{"timestamp": 1758114026719, "changes": [{"type": "MODIFY", "lineNumber": 30, "content": "        targetSdkVersion 36", "oldContent": "        targetSdkVersion 35"}]}, {"timestamp": 1758114061663, "changes": [{"type": "MODIFY", "lineNumber": 29, "content": "        minSdkVersion 23", "oldContent": "        minSdkVersion flutter.minSdkVersion"}]}, {"timestamp": 1758114969133, "changes": [{"type": "MODIFY", "lineNumber": 29, "content": "        minSdkVersion flutter.minSdkVersion", "oldContent": "        minSdkVersion 23"}]}]}}}